import os
import sys
import json
import time
import random
import asyncio
import logging
import argparse
import pyfiglet
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any, Callable
from functools import wraps
import locale
from datetime import datetime
import threading

from pyquotex.expiration import (
    timestamp_to_date,
    get_timestamp_days_ago
)
from pyquotex.utils.processor import (
    process_candles,
    get_color,
    aggregate_candle
)
from pyquotex.config import credentials
from pyquotex.stable_api import Quotex

__author__ = "Cleiton Leonel Creton"
__version__ = "1.0.3"

USER_AGENT = "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/119.0"

# ANSI Color codes for better display
class Colors:
    """ANSI color codes for terminal output."""
    RESET = '\033[0m'
    BOLD = '\033[1m'
    DIM = '\033[2m'

    # Text colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'

    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

    # Background colors
    BG_BLACK = '\033[40m'
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    BG_MAGENTA = '\033[45m'
    BG_CYAN = '\033[46m'
    BG_WHITE = '\033[47m'

def colored_text(text: str, color: str = Colors.RESET, bold: bool = False) -> str:
    """Returns colored text for terminal output."""
    style = Colors.BOLD if bold else ""
    return f"{style}{color}{text}{Colors.RESET}"

def format_profit_percentage(profit: str) -> str:
    """Formats profit percentage with appropriate color."""
    try:
        profit_float = float(profit)
        if profit_float >= 80:
            return colored_text(f"{profit}%", Colors.BRIGHT_GREEN, bold=True)
        elif profit_float >= 70:
            return colored_text(f"{profit}%", Colors.GREEN)
        elif profit_float >= 60:
            return colored_text(f"{profit}%", Colors.YELLOW)
        else:
            return colored_text(f"{profit}%", Colors.RED)
    except (ValueError, TypeError):
        return colored_text("N/A", Colors.DIM)

def is_traditional_forex_pair(asset_name: str) -> bool:
    """
    تحديد ما إذا كان الزوج من أزواج العملات التقليدية
    Determines if the asset is a traditional forex pair
    """
    # قائمة العملات التقليدية المعروفة
    traditional_currencies = {
        'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'NZD',
        'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'TRY', 'ZAR',
        'MXN', 'BRL', 'SGD', 'HKD', 'KRW', 'CNY', 'INR', 'THB',
        'MYR', 'IDR', 'PHP', 'VND', 'PKR', 'BDT', 'LKR', 'NPR',
        'EGP', 'MAD', 'TND', 'DZD', 'NGN', 'KES', 'GHS', 'UGX',
        'COP', 'PEN', 'CLP', 'ARS', 'UYU', 'BOB', 'PYG'
    }

    # قائمة الكلمات المفتاحية للعملات المشفرة والأسهم والمعادن
    crypto_keywords = [
        'Bitcoin', 'Ethereum', 'Litecoin', 'Ripple', 'Cardano', 'Polkadot',
        'Chainlink', 'Dogecoin', 'Shiba', 'Solana', 'Avalanche', 'TRON',
        'Binance', 'Cosmos', 'Axie', 'Floki', 'Decentraland', 'Melania',
        'Trump', 'Dogwifhat', 'Zcash', 'Pepe', 'Arbitrum', 'Bonk',
        'Gala', 'Aptos', 'Dash', 'Hamster', 'Celestia', 'Beam',
        'Notcoin', 'Toncoin'
    ]

    stock_keywords = [
        'McDonald', 'Microsoft', 'Pfizer', 'American Express', 'Boeing',
        'Intel', 'FACEBOOK', 'Johnson', 'Inc', 'Company', 'Corp'
    ]

    commodity_keywords = [
        'Gold', 'Silver', 'UKBrent', 'USCrude', 'Oil', 'Crude'
    ]

    index_keywords = [
        'Dow Jones', 'NASDAQ', 'FTSE', 'S&P', 'CAC', 'IBEX', 'Nikkei',
        'EURO STOXX', 'Hong Kong', 'ASX', 'China A50'
    ]

    # تنظيف اسم الأصل
    clean_name = asset_name.replace('(OTC)', '').replace('_otc', '').strip()

    # فحص إذا كان يحتوي على كلمات مفتاحية للعملات المشفرة أو الأسهم أو المعادن
    for keyword in crypto_keywords + stock_keywords + commodity_keywords + index_keywords:
        if keyword.lower() in clean_name.lower():
            return False

    # فحص إذا كان زوج عملات تقليدي (مثل EUR/USD, GBP/JPY)
    if '/' in clean_name:
        parts = clean_name.split('/')
        if len(parts) == 2:
            base_currency = parts[0].strip().upper()
            quote_currency = parts[1].strip().upper()
            return (base_currency in traditional_currencies and
                   quote_currency in traditional_currencies)

    return False

def filter_traditional_forex_pairs(assets_data: dict) -> dict:
    """
    تصفية البيانات لإظهار أزواج العملات التقليدية فقط
    Filters data to show only traditional forex pairs
    """
    filtered_data = {}
    for asset_name, asset_data in assets_data.items():
        if is_traditional_forex_pair(asset_name):
            filtered_data[asset_name] = asset_data
    return filtered_data

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pyquotex.log')
    ]
)
logger = logging.getLogger(__name__)

LANGUAGE_MESSAGES = {
    "pt_BR": {
        "private_version_ad": (
            "🌟✨ Esta é a versão COMUNITÁRIA da PyQuotex! ✨🌟\n"
            "🔐  Desbloqueie todo o poder e recursos extras com a nossa versão PRIVADA.\n"
            "📤  Para mais funcionalidades e suporte exclusivo, considere uma doação ao projeto.\n"
            "➡️ Contato para doações e acesso à versão privada: https://t.me/pyquotex/852"
        )
    },
    "en_US": {
        "private_version_ad": (
            "🌟✨ This is the COMMUNITY version of PyQuotex! ✨🌟\n"
            "🔐  Unlock full power and extra features with our PRIVATE version.\n"
            "📤  For more functionalities and exclusive support, please consider donating to the project.\n"
            "➡️ Contact for donations and private version access: https://t.me/pyquotex/852"
        )
    }
}


def detect_user_language() -> str:
    """Attempts to detect the user's system language."""
    try:
        system_lang = locale.getlocale()[0]
        if system_lang and system_lang.startswith("pt"):
            return "pt_BR"
        return "en_US"
    except Exception:
        return "en_US"


def ensure_connection(max_attempts: int = 5):
    """Decorator to ensure connection before executing function."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            if not self.client:
                logger.error("Quotex API client not initialized.")
                raise RuntimeError("Quotex API client not initialized.")

            if await self.client.check_connect():
                logger.debug("Already connected. Proceeding with operation.")
                return await func(self, *args, **kwargs)

            logger.info("Establishing connection...")
            check, reason = await self._connect_with_retry(max_attempts)

            if not check:
                logger.error(f"Failed to connect after multiple attempts: {reason}")
                raise ConnectionError(f"Failed to connect: {reason}")

            try:
                result = await func(self, *args, **kwargs)
                return result
            finally:
                if self.client and await self.client.check_connect():
                    await self.client.close()
                    logger.debug("Connection closed after operation.")

        return wrapper

    return decorator


class PyQuotexCLI:
    """PyQuotex CLI application for trading operations."""

    def __init__(self):
        self.client: Optional[Quotex] = None
        self.setup_client()

    def setup_client(self):
        """Initializes the Quotex API client with credentials."""
        try:
            email, password = credentials()
            self.client = Quotex(
                email=email,
                password=password,
                lang="pt"
            )
            logger.info("Quotex client initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize Quotex client: {e}")
            raise

    async def _connect_with_retry(self, attempts: int = 5) -> Tuple[bool, str]:
        """Internal method to attempt connection with retry logic."""
        logger.info("Attempting to connect to Quotex API...")
        check, reason = await self.client.connect()

        if not check:
            for attempt_num in range(1, attempts + 1):
                logger.warning(f"Connection failed. Attempt {attempt_num} of {attempts}.")

                session_file = Path("session.json")
                if session_file.exists():
                    session_file.unlink()
                    logger.debug("Obsolete session file removed.")

                await asyncio.sleep(2)
                check, reason = await self.client.connect()

                if check:
                    logger.info("Reconnected successfully!")
                    break

            if not check:
                logger.error(f"Failed to connect after {attempts} attempts: {reason}")
                return False, reason

        logger.info(f"Connected successfully: {reason}")
        return check, reason

    def display_banner(self):
        """Displays the application banner, including the private version ad."""
        custom_font = pyfiglet.Figlet(font="ansi_shadow")
        ascii_art = custom_font.renderText("PyQuotex")

        user_lang = detect_user_language()
        ad_message = LANGUAGE_MESSAGES.get(user_lang, LANGUAGE_MESSAGES["en_US"])["private_version_ad"]

        banner = f"""{ascii_art}
        Author: {__author__} | Version: {__version__}
        Use with moderation, because management is everything!
        Support: <EMAIL> or +55 (27) 9 9577-2291

        {ad_message}

        """
        print(banner)

    @ensure_connection()
    async def test_connection(self) -> None:
        """Tests the connection to the Quotex API."""
        logger.info("Running connection test.")
        is_connected = await self.client.check_connect()

        if is_connected:
            logger.info("Connection test successful.")
            print("✅ Connection successful!")
        else:
            logger.error("Connection test failed.")
            print("❌ Connection failed!")

    @ensure_connection()
    async def get_balance(self) -> None:
        """Gets the current account balance (practice by default)."""
        logger.info("Getting account balance.")
        self.client.change_account("PRACTICE")
        balance = await self.client.get_balance()
        logger.info(f"Current balance: {balance}")
        print(f"💰 Current Balance: R$ {balance:.2f}")

    @ensure_connection()
    async def get_profile(self) -> None:
        """Gets user profile information."""
        logger.info("Getting user profile.")

        profile = await self.client.get_profile()

        description = (
            f"\n👤 User Profile:\n"
            f"Name: {profile.nick_name}\n"
            f"Demo Balance: R$ {profile.demo_balance:.2f}\n"
            f"Live Balance: R$ {profile.live_balance:.2f}\n"
            f"ID: {profile.profile_id}\n"
            f"Avatar: {profile.avatar}\n"
            f"Country: {profile.country_name}\n"
            f"Time Zone: {profile.offset}\n"
        )
        logger.info("Profile retrieved successfully.")
        print(description)

    @ensure_connection()
    async def buy_simple(self, amount: float = 50, asset: str = "EURUSD_otc",
                         direction: str = "call", duration: int = 60) -> None:
        """Executes a simple buy operation."""
        logger.info(f"Executing simple buy: {amount} on {asset} in {direction} direction for {duration}s.")

        self.client.change_account("PRACTICE")
        asset_name, asset_data = await self.client.get_available_asset(asset, force_open=True)

        if not asset_data or len(asset_data) < 3 or not asset_data[2]:
            logger.error(f"Asset {asset} is closed or invalid.")
            print(f"❌ ERROR: Asset {asset} is closed or invalid.")
            return

        logger.info(f"Asset {asset} is open.")
        status, buy_info = await self.client.buy(
            amount, asset_name, direction, duration, time_mode="TIMER"
        )

        if status:
            logger.info(f"Buy successful: {buy_info}")
            print(f"✅ Buy executed successfully!")
            print(f"Amount: R$ {amount:.2f}")
            print(f"Asset: {asset}")
            print(f"Direction: {direction.upper()}")
            print(f"Duration: {duration}s")
            print(f"Order ID: {buy_info.get('id', 'N/A')}")
        else:
            logger.error(f"Buy failed: {buy_info}")
            print(f"❌ Buy failed: {buy_info}")

        balance = await self.client.get_balance()
        logger.info(f"Current balance: {balance}")
        print(f"💰 Current Balance: R$ {balance:.2f}")

    @ensure_connection()
    async def buy_and_check_win(self, amount: float = 50, asset: str = "EURUSD_otc",
                                direction: str = "put", duration: int = 60) -> None:
        """Executes a buy operation and checks if it was a win or loss."""
        logger.info(
            f"Executing buy and checking result: {amount} on {asset} in {direction} direction for {duration}s.")

        self.client.change_account("PRACTICE")
        balance_before = await self.client.get_balance()
        logger.info(f"Balance before trade: {balance_before}")
        print(f"💰 Balance Before: R$ {balance_before:.2f}")

        asset_name, asset_data = await self.client.get_available_asset(asset, force_open=True)

        if not asset_data or len(asset_data) < 3 or not asset_data[2]:
            logger.error(f"Asset {asset} is closed or invalid.")
            print(f"❌ ERROR: Asset {asset} is closed or invalid.")
            return

        logger.info(f"Asset {asset} is open.")
        status, buy_info = await self.client.buy(amount, asset_name, direction, duration,
                                                 time_mode="TIMER")

        if not status:
            logger.error(f"Buy operation failed: {buy_info}")
            print(f"❌ Buy operation failed! Details: {buy_info}")
            return

        print(f"📊 Trade executed (ID: {buy_info.get('id', 'N/A')}), waiting for result...")
        logger.info(f"Waiting for trade result ID: {buy_info.get('id', 'N/A')}...")

        if await self.client.check_win(buy_info["id"]):
            profit = self.client.get_profit()
            logger.info(f"WIN! Profit: {profit}")
            print(f"🎉 WIN! Profit: R$ {profit:.2f}")
        else:
            loss = self.client.get_profit()
            logger.info(f"LOSS! Loss: {loss}")
            print(f"💔 LOSS! Loss: R$ {loss:.2f}")

        balance_after = await self.client.get_balance()
        logger.info(f"Balance after trade: {balance_after}")
        print(f"💰 Current Balance: R$ {balance_after:.2f}")

    @ensure_connection()
    async def get_candles(self, asset: str = "CHFJPY_otc", period: int = 60,
                          offset: int = 3600) -> None:
        """Gets historical candle data (candlesticks)."""
        logger.info(f"Getting candles for {asset} with period of {period}s.")

        end_from_time = time.time()
        candles = await self.client.get_candles(asset, end_from_time, offset, period)

        if not candles:
            logger.warning("No candles found for the specified asset.")
            print("⚠️ No candles found for the specified asset.")
            return

        if not candles[0].get("open"):
            candles = process_candles(candles, period)

        candles_color = []
        if len(candles) > 0:
            candles_color = [get_color(candle) for candle in candles if 'open' in candle and 'close' in candle]
        else:
            logger.warning("Not enough candle data to determine colors.")

        logger.info(f"Retrieved {len(candles)} candles.")

        print(f"\n📈 Candles (Candlesticks) for {asset} (Period: {period}s):")
        print(f"Total candles: {len(candles)}")
        if candles_color:
            print(f"Colors of last 10 candles: {' '.join(candles_color[-10:])}")
        else:
            print("   Candle colors not available.")

        print("\n   Last 5 candles:")
        for i, candle in enumerate(candles[-5:]):
            color = candles_color[-(5 - i)] if candles_color and (5 - i) <= len(candles_color) else "N/A"
            emoji = "🟢" if color == "green" else ("🔴" if color == "red" else "⚪")
            print(
                f"{emoji} Open: {candle.get('open', 'N/A'):.4f} → Close: {candle.get('close', 'N/A'):.4f} (Time: {time.strftime('%H:%M:%S', time.localtime(candle.get('time', 0)))})")

    @ensure_connection()
    async def get_assets_status(self) -> None:
        """Gets the status of all available assets (open/closed)."""
        logger.info("Getting assets status.")

        print("\n📊 Assets Status:")
        open_count = 0
        closed_count = 0

        all_assets = self.client.get_all_asset_name()
        if not all_assets:
            logger.warning("Could not retrieve assets list.")
            print("⚠️ Could not retrieve assets list.")
            return

        for asset_info in all_assets:
            asset_symbol = asset_info[0]
            asset_display_name = asset_info[1]

            _, asset_open_data = await self.client.check_asset_open(asset_symbol)

            is_open = False
            if asset_open_data and len(asset_open_data) > 2:
                is_open = asset_open_data[2]

            status_text = "OPEN" if is_open else "CLOSED"
            emoji = "🟢" if is_open else "🔴"

            print(f"{emoji} {asset_display_name} ({asset_symbol}): {status_text}")

            if is_open:
                open_count += 1
            else:
                closed_count += 1

            logger.debug(f"Asset {asset_symbol}: {status_text}")

        print(f"\n📈 Summary: {open_count} open assets, {closed_count} closed assets.")

    @ensure_connection()
    async def get_payment_info(self) -> None:
        """Gets payment information (payout) for all assets."""
        logger.info("Getting payment information.")

        all_data = self.client.get_payment()
        if not all_data:
            logger.warning("No payment information found.")
            print("⚠️ No payment information found.")
            return

        print("\n💰 Payment Information (Payout):")
        print("-" * 50)

        for asset_name, asset_data in list(all_data.items())[:10]:
            profit_1m = asset_data.get("profit", {}).get("1M", "N/A")
            profit_5m = asset_data.get("profit", {}).get("5M", "N/A")
            is_open = asset_data.get("open", False)

            status_text = "OPEN" if is_open else "CLOSED"
            emoji = "🟢" if is_open else "🔴"

            print(f"{emoji} {asset_name} - {status_text}")
            print(f"1M Profit: {profit_1m}% | 5M Profit: {profit_5m}%")
            print("-" * 50)

    @ensure_connection()
    async def balance_refill(self, amount: float = 5000) -> None:
        """Refills the practice account balance."""
        logger.info(f"Refilling practice account balance with R$ {amount:.2f}.")

        self.client.change_account("PRACTICE")
        result = await self.client.edit_practice_balance(amount)

        if result:
            logger.info(f"Balance refill successful: {result}")
            print(f"✅ Practice account balance refilled to R$ {amount:.2f} successfully!")
        else:
            logger.error("Balance refill failed.")
            print("❌ Practice account balance refill failed.")

        new_balance = await self.client.get_balance()
        print(f"💰 New Balance: R$ {new_balance:.2f}")

    @ensure_connection()
    async def get_realtime_price(self, asset: str = "EURJPY_otc") -> None:
        """Monitors the real-time price of an asset."""
        logger.info(f"Getting real-time price for {asset}.")

        asset_name, asset_data = await self.client.get_available_asset(asset, force_open=True)

        if not asset_data or len(asset_data) < 3 or not asset_data[2]:
            logger.error(f"Asset {asset} is closed or invalid for real-time monitoring.")
            print(f"❌ ERROR: Asset {asset} is closed or invalid for monitoring.")
            return

        logger.info(f"Asset {asset} is open. Starting real-time price monitoring.")
        await self.client.start_realtime_price(asset, 60)

        print(f"\n📊 Monitoring real-time price for {asset}")
        print("Press Ctrl+C to stop monitoring...")
        print("-" * 60)

        try:
            while True:
                candle_price_data = await self.client.get_realtime_price(asset_name)
                if candle_price_data:
                    latest_data = candle_price_data[-1]
                    timestamp = latest_data['time']
                    price = latest_data['price']
                    formatted_time = time.strftime('%H:%M:%S', time.localtime(timestamp))

                    print(f"📈 {asset} | {formatted_time} | Price: {price:.5f}", end="\r")
                await asyncio.sleep(0.1)
        except KeyboardInterrupt:
            logger.info("Real-time price monitoring interrupted by user.")
            print("\n✅ Real-time monitoring stopped.")
        finally:
            await self.client.stop_realtime_price(asset_name)
            logger.info(f"Real-time price subscription for {asset_name} stopped.")

    @ensure_connection()
    async def get_signal_data(self) -> None:
        """Gets and monitors trading signal data."""
        logger.info("Getting trading signal data.")

        self.client.start_signals_data()
        print("\n📡 Monitoring trading signals...")
        print("Press Ctrl+C to stop monitoring...")
        print("-" * 60)

        try:
            while True:
                signals = self.client.get_signal_data()
                if signals:
                    print(f"🔔 New Signal Received:")
                    print(json.dumps(signals, indent=2,
                                     ensure_ascii=False))
                    print("-" * 60)
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Signal monitoring interrupted by user.")
            print("\n✅ Signal monitoring stopped.")
        finally:
            pass

    async def maintain_persistent_connection(self) -> None:
        """Maintains a persistent connection to Quotex platform with auto-reconnection."""
        logger.info("Starting persistent connection to Quotex platform.")

        reconnect_attempts = 0
        max_reconnect_attempts = 10

        while True:
            try:
                if not self.client:
                    logger.error("Quotex API client not initialized.")
                    break

                # Check if connection is still active
                if not await self.client.check_connect():
                    logger.warning("Connection lost. Attempting to reconnect...")
                    reconnect_attempts += 1

                    if reconnect_attempts > max_reconnect_attempts:
                        logger.error(f"Max reconnection attempts ({max_reconnect_attempts}) reached.")
                        break

                    # Try to reconnect
                    check, reason = await self._connect_with_retry(5)
                    if check:
                        logger.info("Reconnection successful!")
                        reconnect_attempts = 0  # Reset counter on successful reconnection
                    else:
                        logger.error(f"Reconnection failed: {reason}")
                        await asyncio.sleep(10)  # Wait before next attempt
                        continue
                else:
                    reconnect_attempts = 0  # Reset counter if connection is good

                # Connection is active, continue monitoring
                await asyncio.sleep(5)  # Check connection every 5 seconds

            except KeyboardInterrupt:
                logger.info("Persistent connection monitoring stopped by user.")
                break
            except Exception as e:
                logger.error(f"Error in persistent connection: {e}")
                await asyncio.sleep(10)

    async def monitor_assets_continuously(self) -> None:
        """Continuously monitors available assets and their profit percentages."""
        logger.info("Starting continuous asset monitoring.")

        print(colored_text("\n🔄 Continuous Asset Monitoring Started", Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("📊 Monitoring available pairs with profit percentages...", Colors.BRIGHT_BLUE))
        print(colored_text("Press Ctrl+C to stop monitoring...", Colors.YELLOW))
        print(colored_text("=" * 100, Colors.DIM))

        last_update_time = 0
        update_interval = 30  # Update every 30 seconds

        try:
            while True:
                current_time = time.time()

                # Update data every 30 seconds
                if current_time - last_update_time >= update_interval:
                    try:
                        # Clear screen for better display
                        os.system('cls' if os.name == 'nt' else 'clear')

                        # Header with colors
                        print(colored_text("\n🔄 Continuous Asset Monitoring", Colors.BRIGHT_CYAN, bold=True))
                        print(colored_text(f"📅 Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", Colors.BRIGHT_BLUE))
                        print(colored_text("=" * 100, Colors.DIM))

                        # Get payment information for all assets
                        all_data = self.client.get_payment()
                        if not all_data:
                            print("⚠️ No payment information available.")
                            await asyncio.sleep(5)
                            continue

                        # Sort assets by status (open first) and then by profit
                        sorted_assets = []
                        for asset_name, asset_data in all_data.items():
                            profit_1m = asset_data.get("profit", {}).get("1M", 0)
                            profit_5m = asset_data.get("profit", {}).get("5M", 0)
                            is_open = asset_data.get("open", False)

                            # Convert profit to float for sorting
                            try:
                                profit_1m_float = float(profit_1m) if profit_1m != "N/A" else 0
                                profit_5m_float = float(profit_5m) if profit_5m != "N/A" else 0
                            except (ValueError, TypeError):
                                profit_1m_float = 0
                                profit_5m_float = 0

                            sorted_assets.append({
                                'name': asset_name,
                                'is_open': is_open,
                                'profit_1m': profit_1m,
                                'profit_5m': profit_5m,
                                'profit_1m_float': profit_1m_float,
                                'profit_5m_float': profit_5m_float
                            })

                        # Sort: open assets first, then by 1M profit descending
                        sorted_assets.sort(key=lambda x: (-x['is_open'], -x['profit_1m_float']))

                        # Display assets in a formatted table with colors
                        header = f"{'Asset Name':<30} {'Status':<10} {'1M Profit':<15} {'5M Profit':<15} {'Trend':<8}"
                        print(colored_text(header, Colors.BRIGHT_WHITE, bold=True))
                        print(colored_text("─" * 100, Colors.DIM))

                        open_count = 0
                        closed_count = 0
                        high_profit_count = 0

                        for asset in sorted_assets:
                            status_text = "OPEN" if asset['is_open'] else "CLOSED"
                            emoji = "🟢" if asset['is_open'] else "🔴"

                            # Format profit with colors
                            profit_1m_display = format_profit_percentage(asset['profit_1m']) if asset['profit_1m'] != "N/A" else colored_text("N/A", Colors.DIM)
                            profit_5m_display = format_profit_percentage(asset['profit_5m']) if asset['profit_5m'] != "N/A" else colored_text("N/A", Colors.DIM)

                            # Status with colors
                            if asset['is_open']:
                                status_colored = colored_text(status_text, Colors.BRIGHT_GREEN, bold=True)
                                open_count += 1
                            else:
                                status_colored = colored_text(status_text, Colors.RED)
                                closed_count += 1

                            # Trend indicator
                            try:
                                profit_1m_val = float(asset['profit_1m']) if asset['profit_1m'] != "N/A" else 0
                                profit_5m_val = float(asset['profit_5m']) if asset['profit_5m'] != "N/A" else 0

                                if profit_1m_val > profit_5m_val:
                                    trend = colored_text("↗", Colors.GREEN)
                                elif profit_1m_val < profit_5m_val:
                                    trend = colored_text("↘", Colors.RED)
                                else:
                                    trend = colored_text("→", Colors.YELLOW)

                                if profit_1m_val >= 80:
                                    high_profit_count += 1
                            except (ValueError, TypeError):
                                trend = colored_text("─", Colors.DIM)

                            # Asset name with color based on status
                            asset_name_colored = colored_text(asset['name'], Colors.BRIGHT_WHITE if asset['is_open'] else Colors.DIM)

                            print(f"{emoji} {asset_name_colored:<40} {status_colored:<20} {profit_1m_display:<25} {profit_5m_display:<25} {trend:<8}")

                        print(colored_text("─" * 100, Colors.DIM))

                        # Calculate additional statistics
                        total_assets = len(sorted_assets)
                        open_percentage = (open_count / total_assets * 100) if total_assets > 0 else 0

                        # Find best profit assets
                        best_1m_asset = max(sorted_assets, key=lambda x: x['profit_1m_float'], default=None)
                        best_5m_asset = max(sorted_assets, key=lambda x: x['profit_5m_float'], default=None)

                        # Summary with colors and additional stats
                        print(colored_text("📊 STATISTICS", Colors.BRIGHT_MAGENTA, bold=True))
                        print(colored_text("─" * 50, Colors.DIM))

                        summary_line = f"📈 Total Assets: {colored_text(str(total_assets), Colors.BRIGHT_WHITE, bold=True)} | "
                        summary_line += f"Open: {colored_text(str(open_count), Colors.BRIGHT_GREEN, bold=True)} ({open_percentage:.1f}%) | "
                        summary_line += f"Closed: {colored_text(str(closed_count), Colors.RED)} | "
                        summary_line += f"High Profit (≥80%): {colored_text(str(high_profit_count), Colors.BRIGHT_YELLOW, bold=True)}"

                        print(summary_line)

                        # Best profit assets
                        if best_1m_asset and best_1m_asset['profit_1m_float'] > 0:
                            print(f"🏆 Best 1M Profit: {colored_text(best_1m_asset['name'], Colors.BRIGHT_YELLOW, bold=True)} ({format_profit_percentage(best_1m_asset['profit_1m'])})")

                        if best_5m_asset and best_5m_asset['profit_5m_float'] > 0:
                            print(f"🏆 Best 5M Profit: {colored_text(best_5m_asset['name'], Colors.BRIGHT_YELLOW, bold=True)} ({format_profit_percentage(best_5m_asset['profit_5m'])})")

                        print(colored_text("─" * 50, Colors.DIM))
                        print(colored_text(f"🔄 Next update in {update_interval} seconds...", Colors.BRIGHT_BLUE))

                        last_update_time = current_time

                    except Exception as e:
                        logger.error(f"Error updating asset data: {e}")
                        print(f"❌ Error updating data: {e}")

                await asyncio.sleep(1)  # Check every second

        except KeyboardInterrupt:
            logger.info("Continuous asset monitoring stopped by user.")
            print(colored_text("\n✅ Asset monitoring stopped.", Colors.BRIGHT_GREEN, bold=True))
        except Exception as e:
            logger.error(f"Error in continuous asset monitoring: {e}")
            print(colored_text(f"❌ Monitoring error: {e}", Colors.BRIGHT_RED, bold=True))

    async def start_continuous_monitoring(self) -> None:
        """
        بدء المراقبة المستمرة المحسنة مع جميع الميزات الجديدة
        Starts enhanced continuous monitoring with all new features
        """
        logger.info("Starting enhanced continuous monitoring with historical and live data.")

        print(colored_text("\n" + "="*100, Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("🚀 PYQUOTEX ENHANCED FOREX MONITORING SYSTEM", Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("نظام مراقبة الفوركس المحسن", Colors.CYAN))
        print(colored_text("="*100, Colors.BRIGHT_CYAN, bold=True))

        print(colored_text("\n✨ New Features in this version:", Colors.BRIGHT_YELLOW, bold=True))
        print(colored_text("الميزات الجديدة في هذا الإصدار:", Colors.YELLOW))
        print(colored_text("   🔹 Account type selection (Real/Practice)", Colors.CYAN))
        print(colored_text("   🔹 Real-time live candle updates", Colors.CYAN))
        print(colored_text("   🔹 Automatic candle closure and historical data saving", Colors.CYAN))
        print(colored_text("   🔹 Enhanced account balance and trading history display", Colors.CYAN))
        print(colored_text("   🔹 Minimized terminal output for cleaner monitoring", Colors.CYAN))
        print(colored_text("   🔹 Independent functions for balance and trades display", Colors.CYAN))

        print(colored_text("\n" + "="*100, Colors.BRIGHT_CYAN))

        # استخدام النظام المحسن الجديد
        await self.start_enhanced_continuous_monitoring()

    def select_account_type(self) -> str:
        """
        اختيار نوع الحساب (حقيقي أو تجريبي)
        Select account type (real or practice)
        """
        print(colored_text("\n" + "="*80, Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("🏦 Account Type Selection | اختيار نوع الحساب", Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("="*80, Colors.BRIGHT_CYAN, bold=True))

        print(colored_text("\nPlease select your account type:", Colors.BRIGHT_BLUE, bold=True))
        print(colored_text("يرجى اختيار نوع حسابك:", Colors.BRIGHT_BLUE, bold=True))

        print(colored_text("\n1. 💰 REAL Account (Live Trading)", Colors.BRIGHT_GREEN, bold=True))
        print(colored_text("   الحساب الحقيقي (التداول المباشر)", Colors.GREEN))
        print(colored_text("   ⚠️  Warning: Real money will be used", Colors.BRIGHT_RED))
        print(colored_text("   تحذير: سيتم استخدام أموال حقيقية", Colors.RED))

        print(colored_text("\n2. 🎮 PRACTICE Account (Demo Trading)", Colors.BRIGHT_YELLOW, bold=True))
        print(colored_text("   الحساب التجريبي (التداول التجريبي)", Colors.YELLOW))
        print(colored_text("   ✅ Safe: Virtual money only", Colors.BRIGHT_GREEN))
        print(colored_text("   آمن: أموال افتراضية فقط", Colors.GREEN))

        print(colored_text("\n" + "-"*80, Colors.DIM))

        while True:
            try:
                choice = input(colored_text("Enter your choice (1 for REAL, 2 for PRACTICE): ", Colors.BRIGHT_WHITE, bold=True))

                if choice == "1":
                    print(colored_text("\n⚠️  REAL ACCOUNT SELECTED", Colors.BRIGHT_RED, bold=True))
                    print(colored_text("تم اختيار الحساب الحقيقي", Colors.RED))

                    confirm = input(colored_text("Are you sure? This will use real money! (yes/no): ", Colors.BRIGHT_RED, bold=True))
                    if confirm.lower() in ['yes', 'y', 'نعم']:
                        print(colored_text("✅ Real account confirmed", Colors.BRIGHT_GREEN))
                        return "REAL"
                    else:
                        print(colored_text("❌ Selection cancelled, please choose again", Colors.BRIGHT_YELLOW))
                        continue

                elif choice == "2":
                    print(colored_text("\n✅ PRACTICE ACCOUNT SELECTED", Colors.BRIGHT_GREEN, bold=True))
                    print(colored_text("تم اختيار الحساب التجريبي", Colors.GREEN))
                    return "PRACTICE"

                else:
                    print(colored_text("❌ Invalid choice. Please enter 1 or 2", Colors.BRIGHT_RED))
                    print(colored_text("اختيار غير صحيح. يرجى إدخال 1 أو 2", Colors.RED))

            except KeyboardInterrupt:
                print(colored_text("\n\n❌ Selection cancelled by user", Colors.BRIGHT_RED))
                return "PRACTICE"  # Default to practice if cancelled
            except Exception as e:
                print(colored_text(f"❌ Error: {e}", Colors.BRIGHT_RED))
                print(colored_text("Defaulting to PRACTICE account", Colors.YELLOW))
                return "PRACTICE"

    async def get_account_info(self) -> dict:
        """
        جلب معلومات الحساب
        Get account information
        """
        try:
            # جلب معلومات الملف الشخصي
            profile = await self.client.get_profile()

            # جلب الرصيد الحالي
            balance = await self.client.get_balance()

            # تحديد نوع الحساب
            account_type = "PRACTICE" if self.client.api.account_type > 0 else "REAL"

            account_info = {
                "account_type": account_type,
                "balance": balance,
                "profile": {
                    "name": profile.nick_name,
                    "demo_balance": profile.demo_balance,
                    "live_balance": profile.live_balance,
                    "profile_id": profile.profile_id,
                    "country": profile.country_name,
                    "timezone": profile.offset,
                    "avatar": profile.avatar
                },
                "timestamp": datetime.now().isoformat()
            }

            return account_info

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def get_trading_history(self) -> dict:
        """
        جلب سجل الصفقات
        Get trading history
        """
        try:
            # جلب سجل الصفقات من API
            history_data = await self.client.get_history()

            open_trades = []
            closed_trades = []

            if history_data:
                for trade in history_data:
                    trade_info = {
                        "ticket": trade.get("ticket"),
                        "asset": trade.get("asset"),
                        "amount": trade.get("amount"),
                        "direction": trade.get("direction"),
                        "profit_amount": trade.get("profitAmount", 0),
                        "open_time": trade.get("openTime"),
                        "close_time": trade.get("closeTime"),
                        "status": trade.get("status"),
                        "duration": trade.get("duration"),
                        "profit_percentage": trade.get("profitPercentage", 0)
                    }

                    # تصنيف الصفقات حسب الحالة
                    if trade.get("status") == "open" or trade.get("closeTime") is None:
                        open_trades.append(trade_info)
                    else:
                        closed_trades.append(trade_info)

            # حساب الإحصائيات
            total_profit = sum(float(trade.get("profit_amount", 0)) for trade in closed_trades)
            winning_trades = sum(1 for trade in closed_trades if float(trade.get("profit_amount", 0)) > 0)
            losing_trades = sum(1 for trade in closed_trades if float(trade.get("profit_amount", 0)) < 0)

            trading_history = {
                "open_trades": open_trades,
                "closed_trades": closed_trades,
                "total_open": len(open_trades),
                "total_closed": len(closed_trades),
                "statistics": {
                    "total_profit": total_profit,
                    "winning_trades": winning_trades,
                    "losing_trades": losing_trades,
                    "win_rate": (winning_trades / len(closed_trades) * 100) if closed_trades else 0
                },
                "timestamp": datetime.now().isoformat()
            }

            return trading_history

        except Exception as e:
            logger.error(f"Error getting trading history: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def display_account_balance(self) -> None:
        """
        عرض رصيد الحساب بشكل منسق
        Display account balance in a formatted way
        """
        try:
            print(colored_text("\n" + "="*60, Colors.BRIGHT_CYAN, bold=True))
            print(colored_text("💰 Account Balance | رصيد الحساب", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text("="*60, Colors.BRIGHT_CYAN, bold=True))

            # جلب معلومات الحساب
            account_info = await self.get_account_info()

            if "error" in account_info:
                print(colored_text(f"❌ Error getting account info: {account_info['error']}", Colors.BRIGHT_RED))
                return

            account_type = account_info.get('account_type', 'Unknown')
            balance = account_info.get('balance', 0)
            profile = account_info.get('profile', {})

            # عرض نوع الحساب
            if account_type == "REAL":
                print(colored_text(f"🏦 Account Type: REAL (Live Trading)", Colors.BRIGHT_RED, bold=True))
                print(colored_text(f"نوع الحساب: حقيقي (تداول مباشر)", Colors.RED))
            else:
                print(colored_text(f"🎮 Account Type: PRACTICE (Demo Trading)", Colors.BRIGHT_GREEN, bold=True))
                print(colored_text(f"نوع الحساب: تجريبي (تداول تجريبي)", Colors.GREEN))

            # عرض الرصيد
            print(colored_text(f"\n💵 Current Balance: ${balance:,.2f}", Colors.BRIGHT_YELLOW, bold=True))
            print(colored_text(f"الرصيد الحالي: ${balance:,.2f}", Colors.YELLOW))

            # عرض معلومات إضافية من الملف الشخصي
            if profile:
                print(colored_text(f"\n👤 Account Details:", Colors.BRIGHT_BLUE, bold=True))
                print(colored_text(f"   Name: {profile.get('name', 'N/A')}", Colors.CYAN))
                print(colored_text(f"   Demo Balance: ${profile.get('demo_balance', 0):,.2f}", Colors.CYAN))
                print(colored_text(f"   Live Balance: ${profile.get('live_balance', 0):,.2f}", Colors.CYAN))
                print(colored_text(f"   Country: {profile.get('country', 'N/A')}", Colors.CYAN))

            print(colored_text("\n" + "="*60, Colors.BRIGHT_CYAN))

        except Exception as e:
            logger.error(f"Error displaying account balance: {e}")
            print(colored_text(f"❌ Error displaying balance: {e}", Colors.BRIGHT_RED))

    async def display_open_trades(self) -> None:
        """
        عرض الصفقات المفتوحة
        Display open trades
        """
        try:
            print(colored_text("\n" + "="*80, Colors.BRIGHT_GREEN, bold=True))
            print(colored_text("📈 Open Trades | الصفقات المفتوحة", Colors.BRIGHT_GREEN, bold=True))
            print(colored_text("="*80, Colors.BRIGHT_GREEN, bold=True))

            # جلب سجل الصفقات
            trading_history = await self.get_trading_history()

            if "error" in trading_history:
                print(colored_text(f"❌ Error getting trading history: {trading_history['error']}", Colors.BRIGHT_RED))
                return

            open_trades = trading_history.get('open_trades', [])

            if not open_trades:
                print(colored_text("📭 No open trades found", Colors.BRIGHT_YELLOW, bold=True))
                print(colored_text("لا توجد صفقات مفتوحة", Colors.YELLOW))
                print(colored_text("="*80, Colors.BRIGHT_GREEN))
                return

            print(colored_text(f"📊 Total Open Trades: {len(open_trades)}", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text(f"إجمالي الصفقات المفتوحة: {len(open_trades)}", Colors.CYAN))

            for i, trade in enumerate(open_trades, 1):
                print(colored_text(f"\n🔸 Trade #{i}:", Colors.BRIGHT_WHITE, bold=True))
                print(colored_text(f"   Asset: {trade.get('asset', 'N/A')}", Colors.CYAN))
                print(colored_text(f"   Amount: ${trade.get('amount', 0):,.2f}", Colors.YELLOW))
                print(colored_text(f"   Direction: {trade.get('direction', 'N/A')}", Colors.MAGENTA))
                print(colored_text(f"   Open Time: {trade.get('open_time', 'N/A')}", Colors.GREEN))
                print(colored_text(f"   Status: {trade.get('status', 'N/A')}", Colors.BLUE))

            print(colored_text("\n" + "="*80, Colors.BRIGHT_GREEN))

        except Exception as e:
            logger.error(f"Error displaying open trades: {e}")
            print(colored_text(f"❌ Error displaying open trades: {e}", Colors.BRIGHT_RED))

    async def display_closed_trades(self, limit: int = 10) -> None:
        """
        عرض الصفقات المغلقة
        Display closed trades
        """
        try:
            print(colored_text("\n" + "="*80, Colors.BRIGHT_MAGENTA, bold=True))
            print(colored_text(f"📉 Recent Closed Trades (Last {limit}) | الصفقات المغلقة الأخيرة", Colors.BRIGHT_MAGENTA, bold=True))
            print(colored_text("="*80, Colors.BRIGHT_MAGENTA, bold=True))

            # جلب سجل الصفقات
            trading_history = await self.get_trading_history()

            if "error" in trading_history:
                print(colored_text(f"❌ Error getting trading history: {trading_history['error']}", Colors.BRIGHT_RED))
                return

            closed_trades = trading_history.get('closed_trades', [])

            if not closed_trades:
                print(colored_text("📭 No closed trades found", Colors.BRIGHT_YELLOW, bold=True))
                print(colored_text("لا توجد صفقات مغلقة", Colors.YELLOW))
                print(colored_text("="*80, Colors.BRIGHT_MAGENTA))
                return

            # أخذ آخر صفقات محددة
            recent_trades = closed_trades[-limit:] if len(closed_trades) > limit else closed_trades

            print(colored_text(f"📊 Showing {len(recent_trades)} of {len(closed_trades)} total closed trades", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text(f"عرض {len(recent_trades)} من إجمالي {len(closed_trades)} صفقة مغلقة", Colors.CYAN))

            total_profit = 0
            wins = 0
            losses = 0

            for i, trade in enumerate(recent_trades, 1):
                result = trade.get('result', 'N/A')
                profit = trade.get('profit', 0)
                total_profit += profit

                if result == 'win':
                    wins += 1
                    result_color = Colors.BRIGHT_GREEN
                    result_icon = "✅"
                elif result == 'loss':
                    losses += 1
                    result_color = Colors.BRIGHT_RED
                    result_icon = "❌"
                else:
                    result_color = Colors.YELLOW
                    result_icon = "❓"

                print(colored_text(f"\n{result_icon} Trade #{i}:", Colors.BRIGHT_WHITE, bold=True))
                print(colored_text(f"   Asset: {trade.get('asset', 'N/A')}", Colors.CYAN))
                print(colored_text(f"   Amount: ${trade.get('amount', 0):,.2f}", Colors.YELLOW))
                print(colored_text(f"   Direction: {trade.get('direction', 'N/A')}", Colors.MAGENTA))
                print(colored_text(f"   Result: {result}", result_color, bold=True))
                print(colored_text(f"   Profit: ${profit:,.2f}", result_color))
                print(colored_text(f"   Close Time: {trade.get('close_time', 'N/A')}", Colors.GREEN))

            # عرض الإحصائيات
            print(colored_text(f"\n📊 Summary Statistics:", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text(f"   Total Profit/Loss: ${total_profit:,.2f}",
                             Colors.BRIGHT_GREEN if total_profit >= 0 else Colors.BRIGHT_RED, bold=True))
            print(colored_text(f"   Wins: {wins}", Colors.BRIGHT_GREEN))
            print(colored_text(f"   Losses: {losses}", Colors.BRIGHT_RED))
            if wins + losses > 0:
                win_rate = (wins / (wins + losses)) * 100
                print(colored_text(f"   Win Rate: {win_rate:.1f}%", Colors.BRIGHT_YELLOW))

            print(colored_text("\n" + "="*80, Colors.BRIGHT_MAGENTA))

        except Exception as e:
            logger.error(f"Error displaying closed trades: {e}")
            print(colored_text(f"❌ Error displaying closed trades: {e}", Colors.BRIGHT_RED))

    async def display_complete_account_overview(self) -> None:
        """
        عرض نظرة شاملة على الحساب
        Display complete account overview
        """
        try:
            print(colored_text("\n" + "="*100, Colors.BRIGHT_CYAN, bold=True))
            print(colored_text("🏦 COMPLETE ACCOUNT OVERVIEW | نظرة شاملة على الحساب", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text("="*100, Colors.BRIGHT_CYAN, bold=True))

            # عرض الرصيد
            await self.display_account_balance()

            # عرض الصفقات المفتوحة
            await self.display_open_trades()

            # عرض الصفقات المغلقة الأخيرة
            await self.display_closed_trades(limit=5)

            print(colored_text("\n" + "="*100, Colors.BRIGHT_CYAN, bold=True))
            print(colored_text("✅ Account overview completed | تم عرض نظرة شاملة على الحساب", Colors.BRIGHT_GREEN, bold=True))
            print(colored_text("="*100, Colors.BRIGHT_CYAN, bold=True))

        except Exception as e:
            logger.error(f"Error displaying complete account overview: {e}")
            print(colored_text(f"❌ Error displaying account overview: {e}", Colors.BRIGHT_RED))

    def save_data_to_json(self, data: dict, filename: str = None) -> str:
        """
        حفظ البيانات في ملف JSON
        Save data to JSON file
        """
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"data/quotex_data_{timestamp}.json"

            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"Data saved to {filename}")
            return filename

        except Exception as e:
            logger.error(f"Error saving data to JSON: {e}")
            return None

    async def collect_and_save_data(self) -> dict:
        """
        جمع جميع البيانات وحفظها في JSON
        Collect all data and save to JSON
        """
        try:
            # جلب معلومات الحساب
            account_info = await self.get_account_info()

            # جلب سجل الصفقات
            trading_history = await self.get_trading_history()

            # جلب بيانات الأزواج
            all_payment_data = self.client.get_payment()

            # تصفية أزواج العملات التقليدية فقط
            filtered_forex_data = filter_traditional_forex_pairs(all_payment_data) if all_payment_data else {}

            # تنظيم البيانات
            sorted_forex_pairs = []
            if filtered_forex_data:
                for asset_name, asset_data in filtered_forex_data.items():
                    profit_1m = asset_data.get("profit", {}).get("1M", 0)
                    profit_5m = asset_data.get("profit", {}).get("5M", 0)
                    is_open = asset_data.get("open", False)

                    try:
                        profit_1m_float = float(profit_1m) if profit_1m != "N/A" else 0
                        profit_5m_float = float(profit_5m) if profit_5m != "N/A" else 0
                    except (ValueError, TypeError):
                        profit_1m_float = 0
                        profit_5m_float = 0

                    sorted_forex_pairs.append({
                        'name': asset_name,
                        'is_open': is_open,
                        'profit_1m': profit_1m,
                        'profit_5m': profit_5m,
                        'profit_1m_float': profit_1m_float,
                        'profit_5m_float': profit_5m_float,
                        'turbo_payment': asset_data.get("turbo_payment", "N/A"),
                        'payment': asset_data.get("payment", "N/A")
                    })

                # ترتيب حسب الحالة والربح
                sorted_forex_pairs.sort(key=lambda x: (-x['is_open'], -x['profit_1m_float']))

            # إحصائيات
            total_pairs = len(sorted_forex_pairs)
            open_pairs = sum(1 for pair in sorted_forex_pairs if pair['is_open'])
            closed_pairs = total_pairs - open_pairs
            high_profit_pairs = sum(1 for pair in sorted_forex_pairs if pair['profit_1m_float'] >= 80)

            # البيانات النهائية
            final_data = {
                "collection_info": {
                    "timestamp": datetime.now().isoformat(),
                    "data_type": "traditional_forex_pairs_only",
                    "total_pairs": total_pairs,
                    "open_pairs": open_pairs,
                    "closed_pairs": closed_pairs,
                    "high_profit_pairs": high_profit_pairs
                },
                "account_info": account_info,
                "trading_history": trading_history,
                "forex_pairs": sorted_forex_pairs,
                "statistics": {
                    "total_assets": total_pairs,
                    "open_assets": open_pairs,
                    "closed_assets": closed_pairs,
                    "open_percentage": (open_pairs / total_pairs * 100) if total_pairs > 0 else 0,
                    "high_profit_assets": high_profit_pairs,
                    "best_1m_profit": max(sorted_forex_pairs, key=lambda x: x['profit_1m_float'], default=None),
                    "best_5m_profit": max(sorted_forex_pairs, key=lambda x: x['profit_5m_float'], default=None)
                }
            }

            return final_data

        except Exception as e:
            logger.error(f"Error collecting data: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def monitor_and_save_forex_data(self, save_interval: int = 300) -> None:
        """
        مراقبة أزواج العملات التقليدية وحفظها في JSON
        Monitor traditional forex pairs and save to JSON
        """
        logger.info("Starting forex monitoring with JSON saving.")

        print(colored_text("\n🔄 Forex Monitoring with JSON Saving Started", Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("💾 Data will be saved to JSON files automatically", Colors.BRIGHT_BLUE))
        print(colored_text("📊 Monitoring traditional forex pairs only", Colors.BRIGHT_GREEN))
        print(colored_text("Press Ctrl+C to stop monitoring...", Colors.YELLOW))
        print(colored_text("=" * 100, Colors.DIM))

        last_save_time = 0
        save_counter = 0

        try:
            while True:
                current_time = time.time()

                # حفظ البيانات كل فترة محددة (افتراضياً كل 5 دقائق)
                if current_time - last_save_time >= save_interval:
                    try:
                        print(colored_text(f"\n📊 Collecting data... (Save #{save_counter + 1})", Colors.BRIGHT_MAGENTA, bold=True))

                        # جمع البيانات
                        data = await self.collect_and_save_data()

                        # حفظ البيانات
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"data/forex_monitoring/forex_data_{timestamp}.json"
                        saved_file = self.save_data_to_json(data, filename)

                        if saved_file:
                            save_counter += 1
                            print(colored_text(f"✅ Data saved successfully to: {saved_file}", Colors.BRIGHT_GREEN, bold=True))

                            # عرض ملخص سريع
                            if 'statistics' in data:
                                stats = data['statistics']
                                print(colored_text(f"📈 Quick Summary:", Colors.BRIGHT_WHITE, bold=True))
                                print(colored_text(f"   • Total Forex Pairs: {stats.get('total_assets', 0)}", Colors.WHITE))
                                print(colored_text(f"   • Open Pairs: {stats.get('open_assets', 0)}", Colors.GREEN))
                                print(colored_text(f"   • High Profit Pairs (≥80%): {stats.get('high_profit_assets', 0)}", Colors.YELLOW))

                                if stats.get('best_1m_profit'):
                                    best_pair = stats['best_1m_profit']
                                    print(colored_text(f"   • Best 1M Profit: {best_pair['name']} ({best_pair['profit_1m']}%)", Colors.BRIGHT_YELLOW))

                            # معلومات الحساب
                            if 'account_info' in data and 'profile' in data['account_info']:
                                account = data['account_info']
                                print(colored_text(f"💰 Account: {account.get('account_type', 'N/A')} | Balance: {account.get('balance', 'N/A')}", Colors.BRIGHT_BLUE))
                        else:
                            print(colored_text("❌ Failed to save data", Colors.BRIGHT_RED, bold=True))

                        last_save_time = current_time

                        # عرض العد التنازلي للحفظ التالي
                        next_save_in = save_interval
                        print(colored_text(f"⏰ Next save in {next_save_in} seconds...", Colors.DIM))

                    except Exception as e:
                        logger.error(f"Error during data collection and saving: {e}")
                        print(colored_text(f"❌ Error during data collection: {e}", Colors.BRIGHT_RED))

                # عرض العد التنازلي
                time_until_next_save = save_interval - (current_time - last_save_time)
                if time_until_next_save > 0:
                    print(colored_text(f"\r⏳ Next save in {int(time_until_next_save)} seconds... (Total saves: {save_counter})", Colors.DIM), end="", flush=True)

                await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("Forex monitoring with JSON saving stopped by user.")
            print(colored_text(f"\n✅ Monitoring stopped. Total saves completed: {save_counter}", Colors.BRIGHT_GREEN, bold=True))
        except Exception as e:
            logger.error(f"Error in forex monitoring: {e}")
            print(colored_text(f"\n❌ Monitoring error: {e}", Colors.BRIGHT_RED, bold=True))

    async def integrated_forex_system(self) -> None:
        """
        النظام المتكامل لمراقبة أزواج العملات
        Integrated forex monitoring system
        """
        logger.info("Starting integrated forex monitoring system.")

        # التأكد من الاتصال أولاً
        if not await self.client.check_connect():
            logger.info("Establishing initial connection...")
            check, reason = await self._connect_with_retry(5)
            if not check:
                logger.error(f"Failed to establish initial connection: {reason}")
                print(colored_text(f"❌ Failed to connect: {reason}", Colors.BRIGHT_RED, bold=True))
                return

        print(colored_text("✅ Connected to Quotex platform successfully!", Colors.BRIGHT_GREEN, bold=True))
        print(colored_text("🔄 Starting integrated forex monitoring system...", Colors.BRIGHT_CYAN, bold=True))

        # تغيير إلى الحساب التجريبي
        self.client.change_account("PRACTICE")

        # إنشاء ملف البيانات الرئيسي
        main_data_file = "data/forex_monitoring/forex_data_main.json"
        os.makedirs(os.path.dirname(main_data_file), exist_ok=True)

        # الخطوة 1: جلب قائمة الأزواج المتاحة وحفظها
        print(colored_text("📊 Step 1: Loading available forex pairs...", Colors.BRIGHT_BLUE, bold=True))
        initial_data = await self.initialize_forex_data()

        if not initial_data:
            print(colored_text("❌ Failed to initialize forex data", Colors.BRIGHT_RED, bold=True))
            return

        # حفظ البيانات الأولية
        self.save_data_to_json(initial_data, main_data_file)
        print(colored_text(f"✅ Initial forex data saved to: {main_data_file}", Colors.BRIGHT_GREEN, bold=True))
        print(colored_text(f"📈 Found {len(initial_data.get('forex_pairs', []))} traditional forex pairs", Colors.BRIGHT_YELLOW))

        # الخطوة 2: بدء التحديث المستمر كل دقيقة
        print(colored_text("🔄 Step 2: Starting continuous updates every minute...", Colors.BRIGHT_BLUE, bold=True))

        # إنشاء مهام للاتصال المستمر والتحديث المستمر
        connection_task = asyncio.create_task(self.maintain_persistent_connection())
        update_task = asyncio.create_task(self.continuous_forex_updates(main_data_file))

        try:
            # تشغيل المهام معاً
            await asyncio.gather(connection_task, update_task)
        except KeyboardInterrupt:
            logger.info("Integrated forex system interrupted by user.")
            print(colored_text("\n✅ System stopped by user.", Colors.BRIGHT_GREEN, bold=True))
        except Exception as e:
            logger.error(f"Error in integrated forex system: {e}")
            print(colored_text(f"\n❌ System error: {e}", Colors.BRIGHT_RED, bold=True))
        finally:
            # إلغاء المهام
            if not connection_task.done():
                connection_task.cancel()
            if not update_task.done():
                update_task.cancel()

            # إغلاق الاتصال
            if self.client and await self.client.check_connect():
                await self.client.close()
                logger.info("Connection closed.")

    async def initialize_forex_data(self) -> dict:
        """
        تهيئة بيانات أزواج العملات الأولية
        Initialize initial forex pairs data
        """
        try:
            # جلب بيانات الأزواج
            all_payment_data = self.client.get_payment()
            if not all_payment_data:
                return None

            # تصفية أزواج العملات التقليدية فقط
            filtered_forex_data = filter_traditional_forex_pairs(all_payment_data)

            # تنظيم البيانات
            forex_pairs = []
            for asset_name, asset_data in filtered_forex_data.items():
                pair_info = {
                    'name': asset_name,
                    'is_open': asset_data.get("open", False),
                    'profit_1m': asset_data.get("profit", {}).get("1M", "N/A"),
                    'profit_5m': asset_data.get("profit", {}).get("5M", "N/A"),
                    'turbo_payment': asset_data.get("turbo_payment", "N/A"),
                    'payment': asset_data.get("payment", "N/A"),
                    'last_update': datetime.now().isoformat()
                }
                forex_pairs.append(pair_info)

            # ترتيب حسب الاسم
            forex_pairs.sort(key=lambda x: x['name'])

            initial_data = {
                "initialization_info": {
                    "timestamp": datetime.now().isoformat(),
                    "total_pairs": len(forex_pairs),
                    "data_type": "traditional_forex_pairs_only"
                },
                "forex_pairs": forex_pairs
            }

            return initial_data

        except Exception as e:
            logger.error(f"Error initializing forex data: {e}")
            return None

    async def continuous_forex_updates(self, forex_file: str) -> None:
        """
        التحديث المستمر لأزواج العملات كل دقيقة
        Continuous forex pairs updates every minute
        """
        logger.info("Starting continuous forex updates every minute.")

        update_counter = 0

        try:
            while True:
                try:
                    update_counter += 1
                    current_time = datetime.now()

                    print(colored_text(f"\n🔄 Update #{update_counter} - {current_time.strftime('%H:%M:%S')}", Colors.BRIGHT_MAGENTA, bold=True))

                    # قراءة البيانات الحالية
                    current_data = {}
                    if os.path.exists(forex_file):
                        with open(forex_file, 'r', encoding='utf-8') as f:
                            current_data = json.load(f)

                    # جلب البيانات المحدثة
                    all_payment_data = self.client.get_payment()
                    if not all_payment_data:
                        print(colored_text("⚠️ No payment data available", Colors.YELLOW))
                        await asyncio.sleep(60)
                        continue

                    # تصفية أزواج العملات التقليدية
                    filtered_forex_data = filter_traditional_forex_pairs(all_payment_data)

                    # تحديث البيانات
                    updated_pairs = []
                    open_count = 0
                    high_profit_count = 0

                    for asset_name, asset_data in filtered_forex_data.items():
                        profit_1m = asset_data.get("profit", {}).get("1M", "N/A")
                        profit_5m = asset_data.get("profit", {}).get("5M", "N/A")
                        is_open = asset_data.get("open", False)

                        # حساب الإحصائيات
                        if is_open:
                            open_count += 1

                        try:
                            if profit_1m != "N/A" and float(profit_1m) >= 80:
                                high_profit_count += 1
                        except (ValueError, TypeError):
                            pass

                        pair_info = {
                            'name': asset_name,
                            'is_open': is_open,
                            'profit_1m': profit_1m,
                            'profit_5m': profit_5m,
                            'turbo_payment': asset_data.get("turbo_payment", "N/A"),
                            'payment': asset_data.get("payment", "N/A"),
                            'last_update': current_time.isoformat()
                        }
                        updated_pairs.append(pair_info)

                    # ترتيب حسب الاسم
                    updated_pairs.sort(key=lambda x: x['name'])

                    # تحديث البيانات الكاملة
                    updated_data = {
                        "initialization_info": current_data.get("initialization_info", {}),
                        "last_update_info": {
                            "timestamp": current_time.isoformat(),
                            "update_number": update_counter,
                            "total_pairs": len(updated_pairs),
                            "open_pairs": open_count,
                            "closed_pairs": len(updated_pairs) - open_count,
                            "high_profit_pairs": high_profit_count
                        },
                        "forex_pairs": updated_pairs
                    }

                    # حفظ البيانات المحدثة
                    self.save_data_to_json(updated_data, forex_file)

                    # عرض ملخص سريع
                    print(colored_text(f"✅ Updated {len(updated_pairs)} forex pairs", Colors.BRIGHT_GREEN))
                    print(colored_text(f"📊 Open: {open_count} | Closed: {len(updated_pairs) - open_count} | High Profit: {high_profit_count}", Colors.BRIGHT_BLUE))

                    # انتظار دقيقة واحدة
                    print(colored_text("⏰ Next update in 60 seconds...", Colors.DIM))
                    await asyncio.sleep(60)

                except Exception as e:
                    logger.error(f"Error in forex update cycle: {e}")
                    print(colored_text(f"❌ Update error: {e}", Colors.BRIGHT_RED))
                    await asyncio.sleep(60)

        except KeyboardInterrupt:
            logger.info("Continuous forex updates stopped by user.")
            print(colored_text("\n✅ Forex updates stopped.", Colors.BRIGHT_GREEN, bold=True))

    async def save_account_and_trading_data(self) -> None:
        """
        حفظ بيانات الحساب وسجل الصفقات في ملفات منفصلة
        Save account and trading data in separate files
        """
        try:
            # ملف بيانات الحساب
            account_file = "data/forex_monitoring/account_info.json"

            # ملف سجل الصفقات
            trading_file = "data/forex_monitoring/trading_history.json"

            # جلب بيانات الحساب
            account_info = await self.get_account_info()

            # جلب سجل الصفقات
            trading_history = await self.get_trading_history()

            # حفظ بيانات الحساب
            self.save_data_to_json(account_info, account_file)
            print(colored_text(f"💰 Account info saved to: {account_file}", Colors.BRIGHT_CYAN))

            # حفظ سجل الصفقات
            self.save_data_to_json(trading_history, trading_file)
            print(colored_text(f"📊 Trading history saved to: {trading_file}", Colors.BRIGHT_CYAN))

        except Exception as e:
            logger.error(f"Error saving account and trading data: {e}")
            print(colored_text(f"❌ Error saving account/trading data: {e}", Colors.BRIGHT_RED))

    async def start_integrated_forex_system(self) -> None:
        """
        النظام المتكامل لمراقبة أزواج العملات
        Integrated forex monitoring system
        """
        logger.info("Starting integrated forex monitoring system.")

        # التأكد من الاتصال أولاً
        if not await self.client.check_connect():
            logger.info("Establishing initial connection...")
            check, reason = await self._connect_with_retry(5)
            if not check:
                logger.error(f"Failed to establish initial connection: {reason}")
                print(colored_text(f"❌ Failed to connect: {reason}", Colors.BRIGHT_RED, bold=True))
                return

        print(colored_text("✅ Connected to Quotex platform successfully!", Colors.BRIGHT_GREEN, bold=True))
        print(colored_text("🔄 Starting integrated forex monitoring system...", Colors.BRIGHT_CYAN, bold=True))

        # تغيير إلى الحساب التجريبي
        self.client.change_account("PRACTICE")

        # إنشاء مجلد البيانات
        os.makedirs("data/forex_monitoring", exist_ok=True)

        # ملفات البيانات المنفصلة
        forex_pairs_file = "data/forex_monitoring/forex_pairs.json"

        print(colored_text("=" * 80, Colors.DIM))

        # الخطوة 1: جلب قائمة الأزواج المتاحة وحفظها
        print(colored_text("📊 Step 1: Loading available forex pairs...", Colors.BRIGHT_BLUE, bold=True))
        initial_data = await self.initialize_forex_data()

        if not initial_data:
            print(colored_text("❌ Failed to initialize forex data", Colors.BRIGHT_RED, bold=True))
            return

        # حفظ البيانات الأولية
        self.save_data_to_json(initial_data, forex_pairs_file)
        print(colored_text(f"✅ Initial forex pairs saved to: {forex_pairs_file}", Colors.BRIGHT_GREEN, bold=True))
        print(colored_text(f"📈 Found {len(initial_data.get('forex_pairs', []))} traditional forex pairs", Colors.BRIGHT_YELLOW))

        # الخطوة 2: حفظ بيانات الحساب وسجل الصفقات في ملفات منفصلة
        print(colored_text("\n💰 Step 2: Saving account and trading data...", Colors.BRIGHT_BLUE, bold=True))
        await self.save_account_and_trading_data()

        # الخطوة 3: بدء التحديث المستمر كل دقيقة
        print(colored_text("\n🔄 Step 3: Starting continuous updates every minute...", Colors.BRIGHT_BLUE, bold=True))
        print(colored_text("Press Ctrl+C to stop the system...", Colors.YELLOW))
        print(colored_text("=" * 80, Colors.DIM))

        # إنشاء مهام للاتصال المستمر والتحديث المستمر
        connection_task = asyncio.create_task(self.maintain_persistent_connection())
        update_task = asyncio.create_task(self.continuous_forex_updates(forex_pairs_file))

        try:
            # تشغيل المهام معاً
            await asyncio.gather(connection_task, update_task)
        except KeyboardInterrupt:
            logger.info("Integrated forex system interrupted by user.")
            print(colored_text("\n✅ System stopped by user.", Colors.BRIGHT_GREEN, bold=True))
        except Exception as e:
            logger.error(f"Error in integrated forex system: {e}")
            print(colored_text(f"\n❌ System error: {e}", Colors.BRIGHT_RED, bold=True))
        finally:
            # إلغاء المهام
            if not connection_task.done():
                connection_task.cancel()
            if not update_task.done():
                update_task.cancel()

            # إغلاق الاتصال
            if self.client and await self.client.check_connect():
                await self.client.close()
                logger.info("Connection closed.")

            print(colored_text("\n📁 Files created:", Colors.BRIGHT_MAGENTA, bold=True))
            print(colored_text("   • forex_pairs.json - Forex pairs with live updates", Colors.CYAN))
            print(colored_text("   • account_info.json - Account information", Colors.CYAN))
            print(colored_text("   • trading_history.json - Trading history", Colors.CYAN))

    async def monitor_forex_with_json_save(self) -> None:
        """
        مراقبة أزواج العملات التقليدية وحفظها في JSON كل 30 ثانية
        Monitor traditional forex pairs and save to JSON every 30 seconds
        """
        logger.info("Starting forex monitoring with JSON saving every 30 seconds.")

        print(colored_text("\n🔄 Forex Monitoring with JSON Saving", Colors.BRIGHT_CYAN, bold=True))
        print(colored_text("💾 Saving forex pairs data to JSON every 30 seconds", Colors.BRIGHT_BLUE))
        print(colored_text("📊 Traditional forex pairs only", Colors.BRIGHT_GREEN))
        print(colored_text("Press Ctrl+C to stop monitoring...", Colors.YELLOW))
        print(colored_text("=" * 80, Colors.DIM))

        # إنشاء مجلد البيانات
        os.makedirs("data/forex_monitoring", exist_ok=True)

        # ملفات البيانات
        forex_file = "data/forex_monitoring/forex_pairs.json"
        account_file = "data/forex_monitoring/account_info.json"

        last_update_time = 0
        update_interval = 30  # Update every 30 seconds
        update_counter = 0

        # حفظ بيانات الحساب في البداية
        try:
            print(colored_text("💰 Getting account information...", Colors.BRIGHT_BLUE))
            account_info = await self.get_account_info()
            self.save_data_to_json(account_info, account_file)
            print(colored_text(f"✅ Account info saved to: {account_file}", Colors.BRIGHT_GREEN))

            if 'profile' in account_info:
                print(colored_text(f"👤 Account: {account_info.get('account_type', 'N/A')} | Balance: {account_info.get('balance', 'N/A')}", Colors.BRIGHT_CYAN))
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            print(colored_text(f"❌ Error getting account info: {e}", Colors.BRIGHT_RED))

        try:
            while True:
                current_time = time.time()

                # Update data every 30 seconds
                if current_time - last_update_time >= update_interval:
                    try:
                        update_counter += 1
                        timestamp = datetime.now()

                        print(colored_text(f"\n🔄 Update #{update_counter} - {timestamp.strftime('%H:%M:%S')}", Colors.BRIGHT_MAGENTA, bold=True))

                        # Get payment information for all assets
                        all_data = self.client.get_payment()
                        if not all_data:
                            print(colored_text("⚠️ No payment information available", Colors.YELLOW))
                            await asyncio.sleep(5)
                            continue

                        # Filter traditional forex pairs only
                        filtered_forex_data = filter_traditional_forex_pairs(all_data)

                        if not filtered_forex_data:
                            print(colored_text("⚠️ No forex pairs found", Colors.YELLOW))
                            await asyncio.sleep(30)
                            continue

                        # Organize forex pairs data
                        forex_pairs = []
                        open_count = 0
                        high_profit_count = 0

                        for asset_name, asset_data in filtered_forex_data.items():
                            profit_1m = asset_data.get("profit", {}).get("1M", "N/A")
                            profit_5m = asset_data.get("profit", {}).get("5M", "N/A")
                            is_open = asset_data.get("open", False)

                            # Count statistics
                            if is_open:
                                open_count += 1

                            try:
                                if profit_1m != "N/A" and float(profit_1m) >= 80:
                                    high_profit_count += 1
                            except (ValueError, TypeError):
                                pass

                            pair_info = {
                                'name': asset_name,
                                'is_open': is_open,
                                'profit_1m': profit_1m,
                                'profit_5m': profit_5m,
                                'turbo_payment': asset_data.get("turbo_payment", "N/A"),
                                'payment': asset_data.get("payment", "N/A"),
                                'last_update': timestamp.isoformat()
                            }
                            forex_pairs.append(pair_info)

                        # Sort by name
                        forex_pairs.sort(key=lambda x: x['name'])

                        # Create complete data structure
                        forex_data = {
                            "update_info": {
                                "timestamp": timestamp.isoformat(),
                                "update_number": update_counter,
                                "total_pairs": len(forex_pairs),
                                "open_pairs": open_count,
                                "closed_pairs": len(forex_pairs) - open_count,
                                "high_profit_pairs": high_profit_count,
                                "data_type": "traditional_forex_pairs_only"
                            },
                            "forex_pairs": forex_pairs
                        }

                        # Save forex data to JSON
                        self.save_data_to_json(forex_data, forex_file)

                        # Display summary
                        print(colored_text(f"✅ Saved {len(forex_pairs)} forex pairs to: {forex_file}", Colors.BRIGHT_GREEN))
                        print(colored_text(f"📊 Open: {open_count} | Closed: {len(forex_pairs) - open_count} | High Profit (≥80%): {high_profit_count}", Colors.BRIGHT_BLUE))
                        print(colored_text(f"⏰ Next update in {update_interval} seconds...", Colors.DIM))

                        last_update_time = current_time

                    except Exception as e:
                        logger.error(f"Error updating forex data: {e}")
                        print(colored_text(f"❌ Error updating data: {e}", Colors.BRIGHT_RED))

                await asyncio.sleep(1)  # Check every second

        except KeyboardInterrupt:
            logger.info("Forex monitoring with JSON saving stopped by user.")
            print(colored_text(f"\n✅ Monitoring stopped. Total updates: {update_counter}", Colors.BRIGHT_GREEN, bold=True))
            print(colored_text("📁 Files saved:", Colors.BRIGHT_MAGENTA))
            print(colored_text(f"   • {forex_file}", Colors.CYAN))
            print(colored_text(f"   • {account_file}", Colors.CYAN))
        except Exception as e:
            logger.error(f"Error in forex monitoring: {e}")
            print(colored_text(f"❌ Monitoring error: {e}", Colors.BRIGHT_RED, bold=True))

    def convert_asset_name_for_api(self, display_name: str) -> str:
        """تحويل اسم الزوج من صيغة العرض إلى صيغة API"""
        # إزالة المسافات واستبدال / بـ _
        api_name = display_name.replace("/", "").replace(" ", "")

        # إضافة _otc للأزواج التي تحتوي على OTC
        if "(OTC)" in display_name:
            api_name = api_name.replace("(OTC)", "_otc")

        return api_name

    def get_last_candle_timestamp(self, file_path: str) -> Optional[float]:
        """جلب timestamp آخر شمعة من الملف"""
        try:
            if not os.path.exists(file_path):
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if 'historical_data' in data and data['historical_data']:
                # آخر شمعة في القائمة
                last_candle = data['historical_data'][-1]
                # استخدام timestamp أولاً، ثم time كبديل
                timestamp = last_candle.get('timestamp') or last_candle.get('time')
                return float(timestamp) if timestamp else None

        except Exception as e:
            logger.error(f"Error reading last candle timestamp from {file_path}: {e}")

        return None

    async def fetch_historical_data_for_pair(self, pair_name: str, api_name: str, candles_count: int = 500) -> Optional[List[Dict]]:
        """جلب البيانات التاريخية لزوج واحد"""
        try:
            logger.info(f"Fetching historical data for {pair_name} ({api_name})")

            # التحقق من توفر الأصل
            asset_name, asset_data = await self.client.get_available_asset(api_name, force_open=True)
            if not asset_data or len(asset_data) < 3:
                logger.warning(f"Asset {api_name} not available or closed")
                return None

            # جلب البيانات التاريخية
            # 500 شمعة × 5 دقائق = 2500 دقيقة = ~41.67 ساعة
            period = 300  # 5 دقائق بالثواني
            offset = candles_count * period  # إجمالي الوقت المطلوب
            end_from_time = time.time()

            candles = await self.client.get_candles(api_name, end_from_time, offset, period)

            if not candles:
                logger.warning(f"No candles received for {pair_name}")
                return None

            # معالجة البيانات إذا لزم الأمر
            if not candles[0].get("open"):
                candles = process_candles(candles, period)

            # التأكد من أن آخر شمعة مغلقة (ليست الشمعة الحالية)
            if candles:
                # إزالة آخر شمعة فقط إذا كانت الشمعة الحالية (غير مغلقة)
                current_time = time.time()
                last_candle_time = candles[-1].get('time', 0)

                # إذا كانت آخر شمعة ضمن آخر 2.5 دقيقة، فهي شمعة حية (أكثر دقة)
                # نستخدم 2.5 دقيقة بدلاً من 5 دقائق للتأكد من عدم حذف شموع مغلقة
                if current_time - last_candle_time < (period / 2):
                    candles = candles[:-1]
                    logger.info(f"Removed live candle for {pair_name} (time diff: {current_time - last_candle_time:.1f}s)")
                else:
                    logger.info(f"Kept last candle for {pair_name} (time diff: {current_time - last_candle_time:.1f}s)")

            logger.info(f"Successfully fetched {len(candles)} candles for {pair_name}")
            return candles

        except Exception as e:
            logger.error(f"Error fetching historical data for {pair_name}: {e}")
            return None

    def get_missing_candles_timestamps(self, last_timestamp: float) -> List[float]:
        """حساب timestamps الشموع المفقودة بناءً على أوقات الشموع الصحيحة"""
        missing_timestamps = []
        current_time = time.time()

        # البدء من الشمعة التالية بعد آخر شمعة محفوظة
        next_candle_time = last_timestamp + 300

        while next_candle_time < current_time:
            # التحقق من أن هذا الوقت يطابق أوقات الشموع الصحيحة
            dt = datetime.fromtimestamp(next_candle_time)
            if dt.minute % 5 == 0 and dt.second == 0:
                # فحص إذا كانت الشمعة مغلقة
                if self.is_candle_closed(next_candle_time):
                    missing_timestamps.append(next_candle_time)

            next_candle_time += 300

        return missing_timestamps

    async def fetch_historical_data_incremental(self, pair_name: str, api_name: str, file_path: str) -> Optional[List[Dict]]:
        """جلب البيانات التاريخية بشكل تدريجي مع فهم صحيح لأوقات الشموع"""
        try:
            # جلب timestamp آخر شمعة محفوظة
            last_timestamp = self.get_last_candle_timestamp(file_path)

            if last_timestamp is None:
                # لا توجد بيانات سابقة، جلب 500 شمعة كاملة
                logger.info(f"No previous data found for {pair_name}, fetching full historical data")
                return await self.fetch_historical_data_for_pair(pair_name, api_name, 500)

            # حساب الشموع المفقودة بناءً على أوقات الشموع الصحيحة
            missing_timestamps = self.get_missing_candles_timestamps(last_timestamp)

            if not missing_timestamps:
                logger.info(f"Data for {pair_name} is up to date")
                return []

            logger.info(f"Found {len(missing_timestamps)} missing candles for {pair_name}")

            # جلب البيانات الجديدة
            candles_needed = len(missing_timestamps) + 5  # إضافة 5 شموع إضافية للأمان
            period = 300
            offset = candles_needed * period
            end_from_time = time.time()

            candles = await self.client.get_candles(api_name, end_from_time, offset, period)

            if not candles:
                return []

            # معالجة البيانات
            if not candles[0].get("open"):
                candles = process_candles(candles, period)

            # تصفية الشموع الجديدة فقط (بعد آخر timestamp)
            new_candles = []
            for candle in candles:
                candle_time = candle.get('time', 0)
                if candle_time > last_timestamp:
                    # التحقق من أن الشمعة مغلقة
                    if self.is_candle_closed(candle_time):
                        new_candles.append(candle)

            # ترتيب الشموع حسب الوقت
            new_candles.sort(key=lambda x: x.get('time', 0))

            logger.info(f"Found {len(new_candles)} new closed candles for {pair_name}")
            return new_candles

        except Exception as e:
            logger.error(f"Error fetching incremental data for {pair_name}: {e}")
            return None

    def format_candle_data(self, candle: Dict) -> Dict:
        """تنسيق بيانات الشمعة مع تحسين الوقت"""
        try:
            timestamp = candle.get('time', 0)

            # تحويل timestamp إلى تاريخ مقروء
            dt = datetime.fromtimestamp(timestamp)

            formatted_candle = {
                'timestamp': timestamp,
                'datetime': dt.strftime('%Y-%m-%d %H:%M:%S'),
                'date': dt.strftime('%Y-%m-%d'),
                'time': dt.strftime('%H:%M:%S'),
                'open': round(float(candle.get('open', 0)), 5),
                'high': round(float(candle.get('high', 0)), 5),
                'low': round(float(candle.get('low', 0)), 5),
                'close': round(float(candle.get('close', 0)), 5),
                'ticks': candle.get('ticks', 0),
                'volume': candle.get('volume', 0)
            }

            return formatted_candle

        except Exception as e:
            logger.error(f"Error formatting candle data: {e}")
            return candle

    def save_historical_data_to_file(self, pair_name: str, candles: List[Dict], file_path: str, is_incremental: bool = False) -> bool:
        """حفظ البيانات التاريخية في ملف منفصل لكل زوج مع تنسيق محسن"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # تنسيق البيانات
            formatted_candles = [self.format_candle_data(candle) for candle in candles]

            if is_incremental and os.path.exists(file_path):
                # قراءة البيانات الموجودة
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)

                # إضافة البيانات الجديدة
                existing_data['historical_data'].extend(formatted_candles)
                existing_data['last_update'] = datetime.now().isoformat()

                # ترتيب البيانات حسب الوقت
                existing_data['historical_data'].sort(key=lambda x: x.get('timestamp', 0))

                # تحديث الإحصائيات
                if 'statistics' not in existing_data:
                    existing_data['statistics'] = {}

                existing_data['statistics'].update({
                    'total_candles': len(existing_data['historical_data']),
                    'first_candle': existing_data['historical_data'][0]['datetime'] if existing_data['historical_data'] else None,
                    'last_candle': existing_data['historical_data'][-1]['datetime'] if existing_data['historical_data'] else None
                })

                # إزالة total_candles المكرر إذا وجد
                existing_data.pop('total_candles', None)

                data_to_save = existing_data
            else:
                # إنشاء ملف جديد
                data_to_save = {
                    'pair_info': {
                        'name': pair_name,
                        'timeframe': '5m',
                        'period_seconds': 300,
                        'data_type': 'historical_and_live',
                        'timezone': 'UTC',
                        'created_at': datetime.now().isoformat()
                    },
                    'historical_data': formatted_candles,
                    'live_candle': None,  # سيتم تحديثها لاحقاً
                    'statistics': {
                        'total_candles': len(formatted_candles),
                        'first_candle': formatted_candles[0]['datetime'] if formatted_candles else None,
                        'last_candle': formatted_candles[-1]['datetime'] if formatted_candles else None
                    },
                    'last_update': datetime.now().isoformat()
                }

            # حفظ البيانات
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, indent=2, ensure_ascii=False)

            logger.info(f"Saved {len(formatted_candles)} formatted candles for {pair_name} to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving historical data for {pair_name}: {e}")
            return False

    def get_current_candle_start_time(self) -> float:
        """حساب وقت بداية الشمعة الحالية بناءً على أوقات الشموع الصحيحة"""
        current_time = time.time()
        dt = datetime.fromtimestamp(current_time)

        # الدقائق المسموحة للشموع (كل 5 دقائق)
        allowed_minutes = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]

        # العثور على أقرب دقيقة مسموحة (الحالية أو السابقة)
        current_minute = dt.minute
        candle_minute = max([m for m in allowed_minutes if m <= current_minute])

        # إنشاء وقت بداية الشمعة
        candle_start = dt.replace(minute=candle_minute, second=0, microsecond=0)

        return candle_start.timestamp()

    def get_next_candle_start_time(self, current_candle_start: float) -> float:
        """حساب وقت بداية الشمعة التالية"""
        return current_candle_start + 300  # إضافة 5 دقائق

    def is_candle_closed(self, candle_start_time: float) -> bool:
        """فحص إذا كانت الشمعة مغلقة بناءً على وقت بدايتها"""
        current_time = time.time()
        candle_end_time = candle_start_time + 300  # 5 دقائق
        return current_time >= candle_end_time

    async def fetch_live_candle_for_pair(self, pair_name: str, api_name: str) -> Optional[Dict]:
        """جلب الشمعة الحية لزوج واحد مع فهم صحيح لأوقات الشموع"""
        try:
            # التحقق من توفر الأصل
            asset_name, asset_data = await self.client.get_available_asset(api_name, force_open=True)
            if not asset_data or len(asset_data) < 3:
                logger.debug(f"Asset {api_name} not available for {pair_name}")
                return None

            # حساب وقت بداية الشمعة الحالية
            current_candle_start = self.get_current_candle_start_time()
            current_time = time.time()

            # فحص إذا كانت الشمعة الحالية مغلقة (مع هامش 10 ثواني)
            candle_end_time = current_candle_start + 300
            if current_time >= (candle_end_time - 10):
                logger.debug(f"Current candle is closed or about to close for {pair_name}")
                return None

            # جلب البيانات من API مع محاولات متعددة
            period = 300  # 5 دقائق
            max_attempts = 3
            candles = None

            for attempt in range(max_attempts):
                try:
                    offset = period * 5  # 5 شموع للتأكد
                    end_from_time = time.time()
                    candles = await self.client.get_candles(api_name, end_from_time, offset, period)

                    if candles:
                        break

                    if attempt < max_attempts - 1:
                        await asyncio.sleep(1)  # انتظار ثانية واحدة قبل المحاولة التالية

                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed for {pair_name}: {e}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(1)

            if not candles:
                logger.debug(f"No candles received for {pair_name}")
                return None

            # معالجة البيانات
            if not candles[0].get("open"):
                candles = process_candles(candles, period)

            # البحث عن الشمعة الحية الحالية
            live_candle = None
            tolerance = 60  # زيادة التسامح إلى 60 ثانية

            # البحث عن الشمعة التي تطابق وقت بداية الشمعة الحالية
            for candle in candles:
                candle_time = candle.get('time', 0)

                # التحقق من أن هذه الشمعة تطابق الشمعة الحالية
                if abs(candle_time - current_candle_start) <= tolerance:
                    live_candle = candle
                    break

            # إذا لم نجد شمعة مطابقة، نأخذ أحدث شمعة
            if not live_candle and candles:
                # ترتيب الشموع حسب الوقت (الأحدث أولاً)
                sorted_candles = sorted(candles, key=lambda x: x.get('time', 0), reverse=True)
                latest_candle = sorted_candles[0]

                # التحقق من أن الشمعة الأحدث ليست قديمة جداً (أقل من 10 دقائق)
                if current_time - latest_candle.get('time', 0) <= 600:
                    live_candle = latest_candle
                    logger.debug(f"Using latest candle for {pair_name} as live candle")

            if not live_candle:
                logger.debug(f"No suitable live candle found for {pair_name}")
                return None

            # تنسيق الشمعة الحية
            formatted_live_candle = self.format_candle_data(live_candle)

            # حساب معلومات التقدم بناءً على الوقت الفعلي للشمعة
            candle_start_time = live_candle.get('time', current_candle_start)
            elapsed_seconds = current_time - candle_start_time
            progress_percentage = (elapsed_seconds / period) * 100
            remaining_seconds = period - elapsed_seconds
            next_candle_start = candle_start_time + period

            # إضافة معلومات الشمعة الحية
            formatted_live_candle.update({
                'is_live': True,
                'live_status': 'active',
                'progress_percentage': round(min(100, max(0, progress_percentage)), 2),
                'remaining_seconds': round(max(0, remaining_seconds), 1),
                'elapsed_seconds': round(elapsed_seconds, 1),
                'last_update': datetime.now().isoformat(),
                'next_candle_time': datetime.fromtimestamp(next_candle_start).strftime('%Y-%m-%d %H:%M:%S'),
                'candle_start_time': datetime.fromtimestamp(candle_start_time).strftime('%Y-%m-%d %H:%M:%S'),
                'expected_close_time': datetime.fromtimestamp(candle_start_time + period).strftime('%Y-%m-%d %H:%M:%S'),
                'actual_candle_time': candle_start_time,
                'calculated_candle_time': current_candle_start
            })

            return formatted_live_candle

        except Exception as e:
            logger.error(f"Error fetching live candle for {pair_name}: {e}")
            return None

    def update_live_candle_in_file(self, pair_name: str, live_candle: Dict, file_path: str) -> bool:
        """تحديث الشمعة الحية في الملف مع تحسينات"""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"File {file_path} does not exist for live candle update")
                return False

            # قراءة البيانات الموجودة
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # تحديث الشمعة الحية
            data['live_candle'] = live_candle
            data['last_update'] = datetime.now().isoformat()

            # تحديث إحصائيات الشمعة الحية
            if 'live_statistics' not in data:
                data['live_statistics'] = {}

            data['live_statistics'].update({
                'last_price': live_candle.get('close', 0),
                'price_change': 0,  # سيتم حسابه لاحقاً
                'update_count': data['live_statistics'].get('update_count', 0) + 1,
                'last_update_time': datetime.now().isoformat()
            })

            # حساب تغيير السعر مقارنة بآخر شمعة تاريخية
            if data.get('historical_data'):
                last_historical = data['historical_data'][-1]
                last_close = last_historical.get('close', 0)
                current_close = live_candle.get('close', 0)
                data['live_statistics']['price_change'] = round(current_close - last_close, 5)
                data['live_statistics']['price_change_percentage'] = round(((current_close - last_close) / last_close) * 100, 3) if last_close > 0 else 0

            # حفظ البيانات المحدثة
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            logger.error(f"Error updating live candle for {pair_name}: {e}")
            return False

    def check_candle_closure_and_move(self, pair_name: str, file_path: str) -> bool:
        """فحص إغلاق الشمعة الحية ونقلها للبيانات التاريخية بناءً على أوقات الشموع الصحيحة"""
        try:
            if not os.path.exists(file_path):
                return False

            # قراءة البيانات
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            live_candle = data.get('live_candle')
            if not live_candle:
                return False

            # الحصول على وقت بداية الشمعة الحية
            candle_start_time = live_candle.get('actual_candle_time',
                                              live_candle.get('timestamp',
                                                            live_candle.get('time', 0)))

            # فحص إذا كانت الشمعة مغلقة بناءً على الوقت الصحيح (مع هامش 5 ثواني)
            current_time = time.time()
            candle_end_time = candle_start_time + 300

            if current_time >= (candle_end_time - 5):
                # تنظيف الشمعة من البيانات الحية
                historical_candle = live_candle.copy()

                # إزالة البيانات الخاصة بالشمعة الحية
                keys_to_remove = ['is_live', 'live_status', 'progress_percentage',
                                'remaining_seconds', 'last_update', 'next_candle_time',
                                'elapsed_seconds', 'candle_start_time', 'expected_close_time',
                                'actual_candle_time', 'calculated_candle_time']
                for key in keys_to_remove:
                    historical_candle.pop(key, None)

                # إضافة معلومات الإغلاق
                historical_candle['status'] = 'closed'
                historical_candle['closed_at'] = datetime.now().isoformat()

                # التحقق من عدم وجود الشمعة مسبقاً في البيانات التاريخية
                candle_timestamp = historical_candle.get('timestamp', 0)
                existing_candle = None
                for i, existing in enumerate(data['historical_data']):
                    if existing.get('timestamp') == candle_timestamp:
                        existing_candle = i
                        break

                if existing_candle is not None:
                    # تحديث الشمعة الموجودة
                    data['historical_data'][existing_candle] = historical_candle
                    logger.info(f"Updated existing candle in historical data for {pair_name} at {historical_candle['datetime']}")
                else:
                    # إضافة شمعة جديدة للبيانات التاريخية
                    data['historical_data'].append(historical_candle)
                    logger.info(f"Added new candle to historical data for {pair_name} at {historical_candle['datetime']}")

                # ترتيب البيانات التاريخية حسب الوقت
                data['historical_data'].sort(key=lambda x: x.get('timestamp', 0))

                # مسح الشمعة الحية
                data['live_candle'] = None

                # تحديث الإحصائيات
                if 'statistics' not in data:
                    data['statistics'] = {}

                data['statistics'].update({
                    'total_candles': len(data['historical_data']),
                    'last_candle': historical_candle['datetime'],
                    'last_closed_candle': datetime.now().isoformat()
                })

                # إضافة إحصائيات أول شمعة إذا لم تكن موجودة
                if data['historical_data']:
                    data['statistics']['first_candle'] = data['historical_data'][0]['datetime']

                # مسح إحصائيات الشمعة الحية
                data.pop('live_statistics', None)

                data['last_update'] = datetime.now().isoformat()

                # حفظ البيانات المحدثة
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

                return True

            return False

        except Exception as e:
            logger.error(f"Error checking candle closure for {pair_name}: {e}")
            return False

    async def fetch_all_historical_data(self) -> None:
        """جلب البيانات التاريخية لجميع الأزواج المتاحة"""
        try:
            # قراءة قائمة الأزواج
            forex_pairs_file = "data/forex_monitoring/forex_pairs.json"
            if not os.path.exists(forex_pairs_file):
                logger.error("Forex pairs file not found")
                return

            with open(forex_pairs_file, 'r', encoding='utf-8') as f:
                forex_data = json.load(f)

            forex_pairs = forex_data.get('forex_pairs', [])
            if not forex_pairs:
                logger.error("No forex pairs found in file")
                return

            print(colored_text(f"📊 Starting historical data collection for {len(forex_pairs)} pairs...", Colors.BRIGHT_CYAN, bold=True))

            # إنشاء مجلد البيانات التاريخية
            historical_data_dir = "data/forex_monitoring/historical_data"
            os.makedirs(historical_data_dir, exist_ok=True)

            successful_pairs = 0
            failed_pairs = 0

            for i, pair in enumerate(forex_pairs, 1):
                pair_name = pair.get('name')
                if not pair_name:
                    continue

                print(colored_text(f"[{i}/{len(forex_pairs)}] Processing {pair_name}...", Colors.BRIGHT_BLUE))

                # تحويل اسم الزوج لصيغة API
                api_name = self.convert_asset_name_for_api(pair_name)

                # مسار الملف
                safe_filename = pair_name.replace("/", "_").replace(" ", "_").replace("(", "").replace(")", "")
                file_path = os.path.join(historical_data_dir, f"{safe_filename}.json")

                # جلب البيانات بشكل تدريجي
                candles = await self.fetch_historical_data_incremental(pair_name, api_name, file_path)

                if candles is not None:
                    if len(candles) > 0:
                        # حفظ البيانات
                        is_incremental = os.path.exists(file_path)
                        if self.save_historical_data_to_file(pair_name, candles, file_path, is_incremental):
                            successful_pairs += 1
                            print(colored_text(f"✅ {pair_name}: {len(candles)} candles saved", Colors.BRIGHT_GREEN))
                        else:
                            failed_pairs += 1
                            print(colored_text(f"❌ {pair_name}: Failed to save data", Colors.BRIGHT_RED))
                    else:
                        successful_pairs += 1
                        print(colored_text(f"✅ {pair_name}: Data is up to date", Colors.BRIGHT_GREEN))
                else:
                    failed_pairs += 1
                    print(colored_text(f"❌ {pair_name}: Failed to fetch data", Colors.BRIGHT_RED))

                # توقف قصير بين الطلبات
                await asyncio.sleep(0.5)

            print(colored_text(f"\n📊 Historical data collection completed!", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text(f"✅ Successful: {successful_pairs} pairs", Colors.BRIGHT_GREEN))
            print(colored_text(f"❌ Failed: {failed_pairs} pairs", Colors.BRIGHT_RED))
            print(colored_text(f"🔄 Starting live data monitoring in 3 seconds...", Colors.BRIGHT_YELLOW, bold=True))

            # توقف قصير قبل بدء المراقبة الحية
            await asyncio.sleep(3)

        except Exception as e:
            logger.error(f"Error in fetch_all_historical_data: {e}")
            print(colored_text(f"❌ Error fetching historical data: {e}", Colors.BRIGHT_RED))

    async def monitor_live_candles_for_all_pairs(self) -> None:
        """مراقبة البيانات الحية لجميع الأزواج بشكل محسن ومتوازي"""
        try:
            # قراءة قائمة الأزواج
            forex_pairs_file = "data/forex_monitoring/forex_pairs.json"
            if not os.path.exists(forex_pairs_file):
                logger.error("Forex pairs file not found")
                return

            with open(forex_pairs_file, 'r', encoding='utf-8') as f:
                forex_data = json.load(f)

            # فلترة الأزواج المفتوحة فقط
            open_pairs = [pair for pair in forex_data.get('forex_pairs', []) if pair.get('is_open', False)]

            if not open_pairs:
                logger.error("No open forex pairs found")
                return

            print(colored_text(f"🔴 Starting enhanced live monitoring for {len(open_pairs)} open pairs...", Colors.BRIGHT_RED, bold=True))
            print(colored_text("📊 Features: Real-time updates, automatic candle closure, price change tracking", Colors.BRIGHT_BLUE))

            historical_data_dir = "data/forex_monitoring/historical_data"
            last_update_time = {}
            update_interval = 5  # تحديث كل 5 ثواني (أسرع)
            batch_size = 10  # معالجة 10 أزواج في كل دفعة

            # إحصائيات المراقبة
            monitoring_stats = {
                'total_updates': 0,
                'successful_updates': 0,
                'failed_updates': 0,
                'candles_closed': 0,
                'start_time': datetime.now()
            }

            while True:
                current_time = time.time()

                # تقسيم الأزواج إلى دفعات للمعالجة المتوازية
                pairs_to_update = []
                for pair in open_pairs:
                    pair_name = pair.get('name')
                    if not pair_name:
                        continue

                    # فحص إذا كان الوقت مناسب للتحديث
                    last_update = last_update_time.get(pair_name, 0)
                    if current_time - last_update >= update_interval:
                        pairs_to_update.append(pair)
                        last_update_time[pair_name] = current_time

                # معالجة الدفعات
                if pairs_to_update:
                    for i in range(0, len(pairs_to_update), batch_size):
                        batch = pairs_to_update[i:i + batch_size]
                        tasks = [self.process_live_candle_for_pair(pair.get('name'), historical_data_dir) for pair in batch]

                        # تنفيذ المهام بشكل متوازي
                        results = await asyncio.gather(*tasks, return_exceptions=True)

                        # تحديث الإحصائيات
                        monitoring_stats['total_updates'] += len(results)
                        successful_count = sum(1 for result in results if result is True)
                        monitoring_stats['successful_updates'] += successful_count
                        monitoring_stats['failed_updates'] += len(results) - successful_count

                        # عرض النتائج كل 60 تحديث (تقليل الرسائل)
                        if monitoring_stats['total_updates'] % 60 == 0 and monitoring_stats['total_updates'] > 0:
                            timestamp = datetime.now().strftime('%H:%M:%S')
                            uptime = datetime.now() - monitoring_stats['start_time']

                            print(colored_text(f"\n📊 [{timestamp}] Live Monitoring Statistics:", Colors.BRIGHT_CYAN, bold=True))
                            print(colored_text(f"   ⏱️  Uptime: {str(uptime).split('.')[0]}", Colors.CYAN))
                            print(colored_text(f"   ✅ Successful updates: {monitoring_stats['successful_updates']}", Colors.BRIGHT_GREEN))
                            print(colored_text(f"   ❌ Failed updates: {monitoring_stats['failed_updates']}", Colors.BRIGHT_RED))
                            print(colored_text(f"   🔄 Total updates: {monitoring_stats['total_updates']}", Colors.BRIGHT_BLUE))
                            print(colored_text(f"   📈 Success rate: {(monitoring_stats['successful_updates']/monitoring_stats['total_updates']*100):.1f}%", Colors.BRIGHT_MAGENTA))
                            print(colored_text(f"   🔴 Active pairs: {len(open_pairs)}", Colors.BRIGHT_YELLOW))

                        # عرض تحديثات مبسطة كل 30 تحديث (تقليل الرسائل)
                        elif monitoring_stats['total_updates'] % 30 == 0 and successful_count > 0:
                            timestamp = datetime.now().strftime('%H:%M:%S')
                            print(colored_text(f"🔄 [{timestamp}] Updated {successful_count} pairs | Total: {monitoring_stats['total_updates']}", Colors.BRIGHT_MAGENTA))

                        # توقف قصير بين الدفعات
                        await asyncio.sleep(1)

                await asyncio.sleep(2)  # فحص كل ثانيتين

        except KeyboardInterrupt:
            uptime = datetime.now() - monitoring_stats['start_time']
            print(colored_text(f"\n✅ Live monitoring stopped by user after {str(uptime).split('.')[0]}", Colors.BRIGHT_GREEN, bold=True))
            print(colored_text(f"📊 Final Statistics:", Colors.BRIGHT_CYAN, bold=True))
            print(colored_text(f"   Total updates: {monitoring_stats['total_updates']}", Colors.CYAN))
            print(colored_text(f"   Successful: {monitoring_stats['successful_updates']}", Colors.BRIGHT_GREEN))
            print(colored_text(f"   Failed: {monitoring_stats['failed_updates']}", Colors.BRIGHT_RED))
        except Exception as e:
            logger.error(f"Error in live monitoring: {e}")
            print(colored_text(f"❌ Error in live monitoring: {e}", Colors.BRIGHT_RED))

    async def process_live_candle_for_pair(self, pair_name: str, historical_data_dir: str) -> bool:
        """معالجة الشمعة الحية لزوج واحد مع فحص الشموع المفقودة"""
        try:
            # تحويل اسم الزوج لصيغة API
            api_name = self.convert_asset_name_for_api(pair_name)

            # مسار الملف
            safe_filename = pair_name.replace("/", "_").replace(" ", "_").replace("(", "").replace(")", "")
            file_path = os.path.join(historical_data_dir, f"{safe_filename}.json")

            # التأكد من وجود الملف
            if not os.path.exists(file_path):
                logger.warning(f"File {file_path} does not exist for {pair_name}")
                return False

            # فحص إغلاق الشمعة السابقة ونقلها
            candle_moved = self.check_candle_closure_and_move(pair_name, file_path)

            # إذا تم نقل شمعة، فحص الشموع المفقودة
            if candle_moved:
                try:
                    missing_candles = await self.fetch_historical_data_incremental(pair_name, api_name, file_path)
                    if missing_candles and len(missing_candles) > 0:
                        # حفظ الشموع المفقودة
                        self.save_historical_data_to_file(pair_name, missing_candles, file_path, True)
                        logger.info(f"Added {len(missing_candles)} missing candles for {pair_name}")
                except Exception as e:
                    logger.warning(f"Error fetching missing candles for {pair_name}: {e}")

            # جلب الشمعة الحية الجديدة
            live_candle = await self.fetch_live_candle_for_pair(pair_name, api_name)

            if live_candle:
                # تحديث الشمعة الحية في الملف
                success = self.update_live_candle_in_file(pair_name, live_candle, file_path)
                if success:
                    logger.debug(f"Updated live candle for {pair_name}")
                return success
            else:
                # لا توجد شمعة حية، قد تكون الشمعة الحالية مغلقة
                # فحص الشموع المفقودة مرة أخرى
                try:
                    missing_candles = await self.fetch_historical_data_incremental(pair_name, api_name, file_path)
                    if missing_candles and len(missing_candles) > 0:
                        self.save_historical_data_to_file(pair_name, missing_candles, file_path, True)
                        logger.info(f"Added {len(missing_candles)} missing candles for {pair_name} (no live candle)")
                        return True
                except Exception as e:
                    logger.warning(f"Error fetching missing candles (no live) for {pair_name}: {e}")

                # تعيين live_candle إلى null إذا لم نجد شمعة حية
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    if data.get('live_candle') is not None:
                        data['live_candle'] = None
                        data['last_update'] = datetime.now().isoformat()

                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2, ensure_ascii=False)

                        logger.debug(f"Set live_candle to null for {pair_name}")
                except Exception as e:
                    logger.warning(f"Error setting live_candle to null for {pair_name}: {e}")

            return False

        except Exception as e:
            logger.error(f"Error processing live candle for {pair_name}: {e}")
            return False

    async def start_enhanced_continuous_monitoring(self) -> None:
        """بدء المراقبة المستمرة المحسنة مع البيانات التاريخية والحية"""
        logger.info("Starting enhanced continuous monitoring with historical and live data.")

        # التأكد من الاتصال
        if not await self.client.check_connect():
            logger.info("Establishing initial connection...")
            check, reason = await self._connect_with_retry(5)
            if not check:
                logger.error(f"Failed to establish initial connection: {reason}")
                print(f"❌ Failed to connect: {reason}")
                return

        print(colored_text("✅ Connected to Quotex platform successfully!", Colors.BRIGHT_GREEN, bold=True))

        # اختيار نوع الحساب
        account_type = self.select_account_type()
        self.client.change_account(account_type)

        print(colored_text(f"🔄 Starting enhanced continuous monitoring with {account_type} account...", Colors.BRIGHT_CYAN, bold=True))

        try:
            # عرض معلومات الحساب الأولية
            await self.display_complete_account_overview()

            # الخطوة 1: جلب البيانات التاريخية (مرة واحدة فقط)
            print(colored_text("\n📊 Step 1: Fetching historical data for all pairs...", Colors.BRIGHT_BLUE, bold=True))
            await self.fetch_all_historical_data()

            # الخطوة 2: بدء مراقبة البيانات الحية
            print(colored_text("\n🔴 Step 2: Starting live data monitoring...", Colors.BRIGHT_RED, bold=True))
            print(colored_text("📝 Note: Candle readings are now minimized for cleaner output", Colors.BRIGHT_YELLOW))
            print(colored_text("ملاحظة: تم تقليل رسائل قراءات الشموع للحصول على عرض أنظف", Colors.YELLOW))

            # إنشاء مهام متوازية
            connection_task = asyncio.create_task(self.maintain_persistent_connection())
            live_monitoring_task = asyncio.create_task(self.monitor_live_candles_for_all_pairs())

            # تشغيل المهام بشكل متوازي
            await asyncio.gather(connection_task, live_monitoring_task)

        except KeyboardInterrupt:
            logger.info("Enhanced continuous monitoring interrupted by user.")
            print(colored_text("\n✅ Enhanced monitoring stopped by user.", Colors.BRIGHT_GREEN, bold=True))
            print(colored_text("تم إيقاف المراقبة المحسنة بواسطة المستخدم", Colors.GREEN))
        except Exception as e:
            logger.error(f"Error in enhanced continuous monitoring: {e}")
            print(colored_text(f"❌ Enhanced monitoring error: {e}", Colors.BRIGHT_RED))
        finally:
            # عرض ملخص نهائي للحساب
            try:
                print(colored_text("\n📊 Final Account Summary:", Colors.BRIGHT_CYAN, bold=True))
                await self.display_account_balance()
            except Exception as e:
                logger.warning(f"Error displaying final summary: {e}")

            # إغلاق الاتصال
            if self.client and await self.client.check_connect():
                await self.client.close()
                logger.info("Connection closed.")

    async def test_new_features(self) -> None:
        """
        اختبار الميزات الجديدة
        Test new features
        """
        try:
            print(colored_text("\n" + "="*80, Colors.BRIGHT_MAGENTA, bold=True))
            print(colored_text("🧪 TESTING NEW FEATURES | اختبار الميزات الجديدة", Colors.BRIGHT_MAGENTA, bold=True))
            print(colored_text("="*80, Colors.BRIGHT_MAGENTA, bold=True))

            # التأكد من الاتصال
            if not await self.client.check_connect():
                logger.info("Establishing connection for testing...")
                check, reason = await self._connect_with_retry(3)
                if not check:
                    print(colored_text(f"❌ Failed to connect for testing: {reason}", Colors.BRIGHT_RED))
                    return

            print(colored_text("✅ Connection established for testing", Colors.BRIGHT_GREEN))

            # اختبار اختيار نوع الحساب
            print(colored_text("\n🧪 Testing account type selection...", Colors.BRIGHT_BLUE, bold=True))
            account_type = self.select_account_type()
            self.client.change_account(account_type)
            print(colored_text(f"✅ Account type set to: {account_type}", Colors.BRIGHT_GREEN))

            # اختبار عرض الرصيد
            print(colored_text("\n🧪 Testing balance display...", Colors.BRIGHT_BLUE, bold=True))
            await self.display_account_balance()

            # اختبار عرض الصفقات المفتوحة
            print(colored_text("\n🧪 Testing open trades display...", Colors.BRIGHT_BLUE, bold=True))
            await self.display_open_trades()

            # اختبار عرض الصفقات المغلقة
            print(colored_text("\n🧪 Testing closed trades display...", Colors.BRIGHT_BLUE, bold=True))
            await self.display_closed_trades(limit=3)

            # اختبار النظرة الشاملة
            print(colored_text("\n🧪 Testing complete account overview...", Colors.BRIGHT_BLUE, bold=True))
            await self.display_complete_account_overview()

            print(colored_text("\n✅ All new features tested successfully!", Colors.BRIGHT_GREEN, bold=True))
            print(colored_text("تم اختبار جميع الميزات الجديدة بنجاح!", Colors.GREEN))

        except Exception as e:
            logger.error(f"Error testing new features: {e}")
            print(colored_text(f"❌ Error testing features: {e}", Colors.BRIGHT_RED))
        finally:
            if self.client and await self.client.check_connect():
                await self.client.close()


def create_parser() -> argparse.ArgumentParser:
    """Creates and configures the command line argument parser."""
    parser = argparse.ArgumentParser(
        description="PyQuotex CLI - Trading automation tool.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Usage examples:
  python app.py test-connection
  python app.py get-balance
  python app.py buy-simple --amount 100 --asset EURUSD_otc --direction call
  python app.py get-candles --asset GBPUSD --period 300
  python app.py realtime-price --asset EURJPY_otc
  python app.py signals
  python app.py continuous-monitor
  python app.py forex-system
  python app.py test-features
        """
    )

    parser.add_argument(
        "--version",
        action="version",
        version=f"PyQuotex {__version__}"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable detailed logging mode (DEBUG)."
    )

    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress most output except errors."
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    subparsers.add_parser("test-connection", help="Test connection to Quotex API.")

    subparsers.add_parser("get-balance", help="Get current account balance (practice by default).")

    subparsers.add_parser("get-profile", help="Get user profile information.")

    buy_parser = subparsers.add_parser("buy-simple", help="Execute a simple buy operation.")
    buy_parser.add_argument("--amount", type=float, default=50, help="Amount to invest.")
    buy_parser.add_argument("--asset", default="EURUSD_otc", help="Asset to trade.")
    buy_parser.add_argument("--direction", choices=["call", "put"], default="call",
                            help="Trade direction (call for up, put for down).")
    buy_parser.add_argument("--duration", type=int, default=60, help="Duration in seconds.")

    buy_check_parser = subparsers.add_parser("buy-and-check", help="Execute a buy and check win/loss.")
    buy_check_parser.add_argument("--amount", type=float, default=50, help="Amount to invest.")
    buy_check_parser.add_argument("--asset", default="EURUSD_otc", help="Asset to trade.")
    buy_check_parser.add_argument("--direction", choices=["call", "put"], default="put",
                                  help="Trade direction.")
    buy_check_parser.add_argument("--duration", type=int, default=60, help="Duration in seconds.")

    candles_parser = subparsers.add_parser("get-candles", help="Get historical candle data (candlesticks).")
    candles_parser.add_argument("--asset", default="CHFJPY_otc", help="Asset to get candles for.")
    candles_parser.add_argument("--period", type=int, default=60,
                                help="Candle period in seconds (e.g., 60 for 1 minute).")
    candles_parser.add_argument("--offset", type=int, default=3600, help="Offset in seconds to fetch candles.")

    subparsers.add_parser("assets-status", help="Get status (open/closed) of all available assets.")

    subparsers.add_parser("payment-info", help="Get payment information (payout) for all assets.")

    refill_parser = subparsers.add_parser("balance-refill", help="Refill practice account balance.")
    refill_parser.add_argument("--amount", type=float, default=5000, help="Amount to refill practice account.")

    price_parser = subparsers.add_parser("realtime-price", help="Monitor real-time price of an asset.")
    price_parser.add_argument("--asset", default="EURJPY_otc", help="Asset to monitor.")

    subparsers.add_parser("signals", help="Monitor trading signal data.")

    subparsers.add_parser("continuous-monitor", help="Start continuous monitoring of assets with persistent connection.")

    subparsers.add_parser("forex-system", help="Start integrated forex monitoring system with live updates every minute.")

    subparsers.add_parser("test-features", help="Test all new enhanced features (account selection, balance display, etc.).")

    return parser


async def main():
    """Main entry point of the CLI application."""
    parser = create_parser()
    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif args.quiet:
        logging.getLogger().setLevel(logging.ERROR)
    else:
        logging.getLogger().setLevel(logging.INFO)

    cli = PyQuotexCLI()

    if not args.quiet:
        cli.display_banner()
        await asyncio.sleep(1)

    try:
        if args.command == "test-connection":
            await cli.test_connection()
        elif args.command == "get-balance":
            await cli.get_balance()
        elif args.command == "get-profile":
            await cli.get_profile()
        elif args.command == "buy-simple":
            await cli.buy_simple(args.amount, args.asset, args.direction, args.duration)
        elif args.command == "buy-and-check":
            await cli.buy_and_check_win(args.amount, args.asset, args.direction, args.duration)
        elif args.command == "get-candles":
            await cli.get_candles(args.asset, args.period, args.offset)
        elif args.command == "assets-status":
            await cli.get_assets_status()
        elif args.command == "payment-info":
            await cli.get_payment_info()
        elif args.command == "balance-refill":
            await cli.balance_refill(args.amount)
        elif args.command == "realtime-price":
            await cli.get_realtime_price(args.asset)
        elif args.command == "signals":
            await cli.get_signal_data()
        elif args.command == "continuous-monitor":
            await cli.start_continuous_monitoring()
        elif args.command == "forex-system":
            await cli.start_integrated_forex_system()
        elif args.command == "test-features":
            await cli.test_new_features()
        else:
            parser.print_help()

    except KeyboardInterrupt:
        logger.info("CLI operation interrupted by user.")
        print("\n✅ Operation interrupted by user.")
    except ConnectionError as e:
        logger.error(f"Connection error during command execution: {e}")
        print(f"❌ Connection error: {e}")
    except RuntimeError as e:
        logger.error(f"Runtime error: {e}")
        print(f"❌ Error: {e}")
    except Exception as e:
        logger.critical(f"Unexpected error occurred during command execution: {e}", exc_info=True)
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n✅ Program terminated by user.")
        sys.exit(0)
    except Exception as e:
        logger.critical(f"Fatal error in main execution: {e}", exc_info=True)
        print(f"❌ FATAL ERROR: {e}")
        sys.exit(1)
